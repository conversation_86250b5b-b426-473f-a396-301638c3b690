/**
 * Global Apple Music Initialization Manager
 * Ensures Apple Music initializes only once per user session
 * Handles StrictMode and prevents duplicate initialization calls
 */

interface InitializationState {
  userId: string;
  isInitializing: boolean;
  isInitialized: boolean;
  initPromise: Promise<boolean> | null;
  timestamp: number;
  isStrictModeProtected: boolean; // Prevent reset during StrictMode cleanup
  createdAt: number; // Track when state was created
}

class AppleMusicInitManager {
  private static instance: AppleMusicInitManager;
  private initStates: Map<string, InitializationState> = new Map();
  private readonly INIT_TIMEOUT = 30000; // 30 seconds timeout
  private readonly STRICT_MODE_PROTECTION_WINDOW = 500; // 500ms protection window

  static getInstance(): AppleMusicInitManager {
    if (!AppleMusicInitManager.instance) {
      AppleMusicInitManager.instance = new AppleMusicInitManager();
    }
    return AppleMusicInitManager.instance;
  }

  /**
   * Initialize Apple Music for a specific user
   * Returns existing promise if already initializing
   */
  async initializeForUser(
    userId: string, 
    syncFunction: () => Promise<boolean>
  ): Promise<boolean> {
    console.log('🎵 APPLE_MUSIC_INIT: Request for user:', userId);

    const existingState = this.initStates.get(userId);
    const now = Date.now();

    // Check if already initialized
    if (existingState?.isInitialized) {
      console.log('🎵 APPLE_MUSIC_INIT: Already initialized for user:', userId);
      return true;
    }

    // Check if currently initializing
    if (existingState?.isInitializing && existingState.initPromise) {
      console.log('🎵 APPLE_MUSIC_INIT: Already initializing for user:', userId);
      return existingState.initPromise;
    }

    // Check for stale initialization (timeout)
    if (existingState?.isInitializing && (now - existingState.timestamp) > this.INIT_TIMEOUT) {
      console.log('🎵 APPLE_MUSIC_INIT: Stale initialization detected, resetting for user:', userId);
      this.resetUser(userId);
    }

    // Start new initialization
    console.log('🎵 APPLE_MUSIC_INIT: Starting initialization for user:', userId);

    const initPromise = this.performInitialization(userId, syncFunction);

    this.initStates.set(userId, {
      userId,
      isInitializing: true,
      isInitialized: false,
      initPromise,
      timestamp: now,
      isStrictModeProtected: true, // Protect new initialization from immediate reset
      createdAt: now
    });

    return initPromise;
  }

  private async performInitialization(
    userId: string, 
    syncFunction: () => Promise<boolean>
  ): Promise<boolean> {
    try {
      console.log('🎵 APPLE_MUSIC_INIT: Executing sync function for user:', userId);
      
      const result = await syncFunction();
      
      // Update state on success
      const state = this.initStates.get(userId);
      if (state) {
        state.isInitializing = false;
        state.isInitialized = result;
        state.initPromise = null;
        state.timestamp = Date.now();
        state.isStrictModeProtected = false; // No longer need protection after completion
      }

      console.log('🎵 APPLE_MUSIC_INIT: Initialization completed for user:', userId, 'result:', result);
      return result;

    } catch (error) {
      console.error('🎵 APPLE_MUSIC_INIT: Initialization failed for user:', userId, error);
      
      // Reset state on error
      this.resetUser(userId);
      return false;
    }
  }

  /**
   * Check if user is already initialized
   */
  isUserInitialized(userId: string): boolean {
    const state = this.initStates.get(userId);
    return state?.isInitialized === true;
  }

  /**
   * Check if user is currently initializing
   */
  isUserInitializing(userId: string): boolean {
    const state = this.initStates.get(userId);
    const now = Date.now();
    
    if (!state?.isInitializing) return false;
    
    // Check for timeout
    if ((now - state.timestamp) > this.INIT_TIMEOUT) {
      this.resetUser(userId);
      return false;
    }
    
    return true;
  }

  /**
   * Reset initialization state for a user
   * Includes StrictMode protection to prevent premature resets
   */
  resetUser(userId: string, force: boolean = false): void {
    const state = this.initStates.get(userId);

    if (!force && state?.isStrictModeProtected) {
      const timeSinceCreation = Date.now() - state.createdAt;

      if (timeSinceCreation < this.STRICT_MODE_PROTECTION_WINDOW) {
        console.log('🎵 APPLE_MUSIC_INIT: Preventing StrictMode reset for user:', userId, 'after', timeSinceCreation, 'ms');
        // Remove protection but don't reset state
        state.isStrictModeProtected = false;
        return;
      }
    }

    console.log('🎵 APPLE_MUSIC_INIT: Resetting state for user:', userId);
    this.initStates.delete(userId);
  }

  /**
   * Reset all initialization states (useful for logout)
   */
  resetAll(): void {
    console.log('🎵 APPLE_MUSIC_INIT: Resetting all states');
    this.initStates.clear();
  }

  /**
   * Force reset a user (bypasses StrictMode protection)
   */
  forceResetUser(userId: string): void {
    this.resetUser(userId, true);
  }

  /**
   * Get current state for debugging
   */
  getState(userId: string): InitializationState | undefined {
    return this.initStates.get(userId);
  }

  /**
   * Get all states for debugging
   */
  getAllStates(): Map<string, InitializationState> {
    return new Map(this.initStates);
  }
}

export const appleMusicInitManager = AppleMusicInitManager.getInstance();
