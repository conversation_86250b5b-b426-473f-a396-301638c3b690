import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

interface SubscriptionConfig {
  channelName: string;
  table: string;
  event: string;
  filter?: string;
  callback: (payload: any) => void;
}

class RealtimeConnectionManager {
  private static instance: RealtimeConnectionManager;
  private channels: Map<string, RealtimeChannel> = new Map();
  private connectionQueue: SubscriptionConfig[] = [];
  private isProcessingQueue = false;
  private isAuthenticated = false;
  private connectionDelay = 300; // Increased delay between connections
  private pendingSubscriptions: Set<string> = new Set(); // Track pending subscriptions
  private subscriptionAttempts: Map<string, number> = new Map(); // Track retry attempts
  private maxRetryAttempts = 3;

  static getInstance(): RealtimeConnectionManager {
    if (!RealtimeConnectionManager.instance) {
      RealtimeConnectionManager.instance = new RealtimeConnectionManager();
    }
    return RealtimeConnectionManager.instance;
  }

  async setAuthenticationStatus(isAuthenticated: boolean) {
    console.log('🔌 REALTIME_MANAGER: Auth status changed to:', isAuthenticated);
    this.isAuthenticated = isAuthenticated;

    if (isAuthenticated) {
      // Verify that we have a valid session before processing queue
      const hasValidSession = await this.verifyAuthSession();
      if (hasValidSession) {
        // Small delay to ensure auth tokens are fully propagated
        setTimeout(() => {
          this.processQueue();
        }, 500);
      } else {
        console.warn('🔌 REALTIME_MANAGER: No valid session found, delaying queue processing');
        // Retry after a longer delay
        setTimeout(() => {
          this.setAuthenticationStatus(true);
        }, 2000);
      }
    } else {
      this.disconnectAll();
    }
  }

  private async verifyAuthSession(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('🔌 REALTIME_MANAGER: Error verifying session:', error);
        return false;
      }

      const isValid = !!(session?.access_token && session?.user);
      console.log('🔌 REALTIME_MANAGER: Session verification result:', isValid);
      return isValid;
    } catch (error) {
      console.error('🔌 REALTIME_MANAGER: Exception during session verification:', error);
      return false;
    }
  }

  subscribe(config: SubscriptionConfig): () => void {
    console.log('🔌 REALTIME_MANAGER: Queuing subscription for:', config.channelName);

    // Check if already subscribed or pending
    if (this.channels.has(config.channelName)) {
      console.log('🔌 REALTIME_MANAGER: Channel already exists:', config.channelName);
      return () => this.unsubscribe(config.channelName);
    }

    if (this.pendingSubscriptions.has(config.channelName)) {
      console.log('🔌 REALTIME_MANAGER: Subscription already pending:', config.channelName);
      return () => this.unsubscribe(config.channelName);
    }

    // Mark as pending
    this.pendingSubscriptions.add(config.channelName);

    // Add to queue
    this.connectionQueue.push(config);

    // Process queue if authenticated
    if (this.isAuthenticated) {
      this.processQueue();
    }

    // Return unsubscribe function
    return () => {
      this.unsubscribe(config.channelName);
    };
  }

  private async processQueue() {
    if (this.isProcessingQueue || this.connectionQueue.length === 0) {
      return;
    }

    console.log('🔌 REALTIME_MANAGER: Processing queue with', this.connectionQueue.length, 'subscriptions');
    this.isProcessingQueue = true;

    try {
      while (this.connectionQueue.length > 0) {
        const config = this.connectionQueue.shift()!;
        await this.createSubscription(config);
        
        // Add delay between connections to prevent overload
        if (this.connectionQueue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, this.connectionDelay));
        }
      }
    } catch (error) {
      console.error('🔌 REALTIME_MANAGER: Error processing queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  private async createSubscription(config: SubscriptionConfig, retryCount = 0): Promise<void> {
    const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff

    try {
      console.log('🔌 REALTIME_MANAGER: Creating subscription for:', config.channelName, retryCount > 0 ? `(retry ${retryCount})` : '');

      // Check if channel already exists
      if (this.channels.has(config.channelName)) {
        console.log('🔌 REALTIME_MANAGER: Channel already exists:', config.channelName);
        this.pendingSubscriptions.delete(config.channelName);
        return;
      }

      // Track retry attempts
      this.subscriptionAttempts.set(config.channelName, retryCount);

      const channel = supabase
        .channel(config.channelName)
        .on('postgres_changes', {
          event: config.event as any,
          schema: 'public',
          table: config.table,
          filter: config.filter
        }, (payload) => {
          console.log('🔌 REALTIME_MANAGER: Received event for:', config.channelName);
          config.callback(payload);
        })
        .subscribe((status) => {
          console.log('🔌 REALTIME_MANAGER: Subscription status for', config.channelName, ':', status);

          if (status === 'CHANNEL_ERROR' && retryCount < this.maxRetryAttempts) {
            console.log('🔌 REALTIME_MANAGER: Channel error, scheduling retry for:', config.channelName);
            setTimeout(() => {
              this.unsubscribe(config.channelName);
              this.createSubscription(config, retryCount + 1);
            }, retryDelay);
          } else if (status === 'SUBSCRIBED') {
            // Successfully subscribed, remove from pending
            this.pendingSubscriptions.delete(config.channelName);
            this.subscriptionAttempts.delete(config.channelName);
          }
        });

      this.channels.set(config.channelName, channel);
      console.log('✅ REALTIME_MANAGER: Successfully created subscription:', config.channelName);

    } catch (error) {
      console.error('🔌 REALTIME_MANAGER: Error creating subscription for', config.channelName, ':', error);

      if (retryCount < this.maxRetryAttempts) {
        console.log('🔌 REALTIME_MANAGER: Scheduling retry for:', config.channelName, 'in', retryDelay, 'ms');
        setTimeout(() => {
          this.createSubscription(config, retryCount + 1);
        }, retryDelay);
      } else {
        console.error('🔌 REALTIME_MANAGER: Max retries exceeded for:', config.channelName);
        this.pendingSubscriptions.delete(config.channelName);
        this.subscriptionAttempts.delete(config.channelName);
      }
    }
  }

  unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName);
    if (channel) {
      console.log('🔌 REALTIME_MANAGER: Unsubscribing from:', channelName);
      supabase.removeChannel(channel);
      this.channels.delete(channelName);
    }

    // Clean up tracking
    this.pendingSubscriptions.delete(channelName);
    this.subscriptionAttempts.delete(channelName);

    // Also remove from queue if not yet processed
    this.connectionQueue = this.connectionQueue.filter(config => config.channelName !== channelName);
  }

  disconnectAll() {
    console.log('🔌 REALTIME_MANAGER: Disconnecting all channels');

    // Clear queue and tracking
    this.connectionQueue = [];
    this.pendingSubscriptions.clear();
    this.subscriptionAttempts.clear();

    // Disconnect all active channels
    for (const [channelName, channel] of this.channels) {
      console.log('🔌 REALTIME_MANAGER: Removing channel:', channelName);
      supabase.removeChannel(channel);
    }

    this.channels.clear();
    this.isProcessingQueue = false;
  }

  getActiveChannels(): string[] {
    return Array.from(this.channels.keys());
  }
}

export const realtimeManager = RealtimeConnectionManager.getInstance();
