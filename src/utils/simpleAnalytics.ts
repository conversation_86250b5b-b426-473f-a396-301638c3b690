import { isAnalyticsAllowed } from './cookieHelpers';

// Google Analytics Measurement ID
const GA_MEASUREMENT_ID = 'G-654G45MZVM'; // Your Google Analytics Measurement ID

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

/**
 * Loads Google Analytics if consent is given
 */
export const loadGoogleAnalytics = (): void => {
  if (!isBrowser) return;

  if (!isAnalyticsAllowed()) {
    console.log('Analytics cookies not allowed. Google Analytics not loaded.');
    return;
  }

  // Check if Google Analytics is already loaded
  if (window.gtag) {
    console.log('Google Analytics already loaded.');
    return;
  }

  try {
    // Add Google Analytics directly to the page
    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    window.gtag('js', new Date());
    window.gtag('config', GA_MEASUREMENT_ID, { 'send_page_view': false });

    // Load the script asynchronously
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;

    // Add timeout to prevent hanging
    const timeout = setTimeout(() => {
      console.warn('Google Analytics script loading timed out (this is normal if ad blockers are enabled or in restricted networks)');
    }, 10000); // 10 second timeout

    // Add error handling for the script
    script.onerror = (e) => {
      clearTimeout(timeout);
      console.warn('Google Analytics script could not be loaded (this is normal if ad blockers are enabled or in restricted networks)');
      // Don't throw an error, just log it as a warning
    };

    script.onload = () => {
      clearTimeout(timeout);
      console.log('Google Analytics script loaded successfully.');
    };

    document.head.appendChild(script);
    console.log('Google Analytics script added to page.');
  } catch (error) {
    console.warn('Error setting up Google Analytics (continuing without analytics):', error);
  }
};

/**
 * Initializes analytics based on current consent
 */
export const initAnalytics = (): void => {
  if (!isBrowser) return;

  // Load Google Analytics if consent is given
  loadGoogleAnalytics();
};

/**
 * Updates analytics based on consent changes
 */
export const updateAnalyticsConsent = (allowed: boolean): void => {
  if (allowed) {
    loadGoogleAnalytics();
  } else if (window.gtag) {
    // Optionally disable analytics if it was previously loaded
    console.log('Analytics consent revoked. Consider reloading the page to fully disable analytics.');
  }
};

/**
 * Track a page view
 */
export const trackPageView = (path: string): void => {
  if (!isBrowser || !isAnalyticsAllowed()) return;

  if (!window.gtag) {
    console.log('Google Analytics not available, skipping page view tracking');
    return;
  }

  try {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path
    });
    console.log(`Page view tracked: ${path}`);
  } catch (error) {
    console.warn('Failed to track page view:', error);
  }
};

/**
 * Track an event
 */
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
): void => {
  if (!isBrowser || !isAnalyticsAllowed()) return;

  if (!window.gtag) {
    console.log('Google Analytics not available, skipping event tracking');
    return;
  }

  try {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value
    });
    console.log(`Event tracked: ${action} (${category})`);
  } catch (error) {
    console.warn('Failed to track event:', error);
  }
};

// Common events for PlayBeg
export const trackEvents = {
  // User events
  userSignUp: () => {
    trackEvent('sign_up', 'User');
  },
  userLogin: () => {
    trackEvent('login', 'User');
  },

  // DJ events
  createSession: (sessionName: string) => {
    trackEvent('create_session', 'DJ', sessionName);
  },
  endSession: (sessionName: string, duration: number) => {
    trackEvent('end_session', 'DJ', sessionName, duration);
  },

  // Song request events
  submitRequest: (sessionId: string) => {
    trackEvent('submit_request', 'Song Requests', sessionId);
  },
  approveRequest: (sessionId: string) => {
    trackEvent('approve_request', 'Song Requests', sessionId);
  },
  declineRequest: (sessionId: string) => {
    trackEvent('decline_request', 'Song Requests', sessionId);
  },

  // Subscription events
  viewPricingPlan: (planName: string) => {
    trackEvent('view_plan', 'Subscriptions', planName);
  },
  startSubscription: (planName: string, price: number) => {
    trackEvent('start_subscription', 'Subscriptions', planName, price);
  },

  // Sponsor module events
  configureSponsor: (sessionId: string) => {
    trackEvent('configure_sponsor', 'Sponsor Module', sessionId);
  }
};

// Define gtag function type for TypeScript
declare global {
  interface Window {
    gtag: (
      command: string,
      action: string,
      params?: Record<string, any>
    ) => void;
    dataLayer: any[];
  }
}
