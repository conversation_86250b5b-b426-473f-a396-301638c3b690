import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { DJProfile } from '@/lib/types';

interface AuthState {
  user: User | null;
  loading: boolean;
  isLoading: boolean; // Alias for loading to match ProtectedRoute
  needsOnboarding: boolean;
  session: Session | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setNeedsOnboarding: (needs: boolean) => void;
  setSession: (session: Session | null) => void;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
  completeOnboarding: () => Promise<void>;
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      loading: true,
      isLoading: true,
      needsOnboarding: false,
      session: null,

      setUser: (user) => {
        console.log('🔍 AUTH_STORE: Setting user:', user?.id || 'null');
        set({ user, isLoading: false, loading: false });
      },

      setLoading: (loading) => {
        set({ loading, isLoading: loading });
      },

      setNeedsOnboarding: (needsOnboarding) => {
        set({ needsOnboarding });
      },

      setSession: (session) => {
        set({ session });
      },

      signIn: async (email: string, password: string) => {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (data?.user && data?.session) {
          set({ 
            user: data.user, 
            session: data.session,
            loading: false,
            isLoading: false 
          });
          console.log('✅ AUTH_STORE: signIn() completed');
        }

        return { data, error };
      },

      signUp: async (email: string, password: string) => {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
          options: {
            data: {
              display_name: email.split('@')[0],
            }
          }
        });
        return { data, error };
      },

      signOut: async () => {
        console.log('🔍 AUTH_STORE: Signing out user');

        const { error } = await supabase.auth.signOut();

        if (!error) {
          set({
            user: null,
            session: null,
            needsOnboarding: false,
            loading: false,
            isLoading: false
          });
        }

        return { error };
      },

      completeOnboarding: async () => {
        const { user } = get();
        if (!user) return;

        try {
          const { error } = await supabase.rpc('complete_onboarding', {
            user_id: user.id
          });

          if (error) throw error;
          
          set({ needsOnboarding: false });
          console.log('✅ AUTH_STORE: Onboarding completed');
        } catch (error) {
          console.error('Error completing onboarding:', error);
          throw error;
        }
      },

      initialize: async () => {
        console.log('🔍 AUTH_STORE: Initializing auth state');

        // Check current state before initialization
        const currentState = get();
        console.log('🔍 AUTH_STORE: Current state before init:', {
          hasUser: !!currentState.user,
          hasSession: !!currentState.session,
          loading: currentState.loading,
          isLoading: currentState.isLoading
        });

        try {
          // Get current session
          const { data: { session }, error } = await supabase.auth.getSession();

          if (error) {
            console.error('🔍 AUTH_STORE: Error getting session:', error);
            set({ user: null, session: null, loading: false, isLoading: false });
            return;
          }

          if (session?.user) {
            console.log('🔍 AUTH_STORE: Found existing session for user:', session.user.id);
            set({
              user: session.user,
              session,
              loading: false,
              isLoading: false
            });

            // Check onboarding status
            await checkOnboardingStatus(session.user.id);
          } else {
            console.log('🔍 AUTH_STORE: No existing session found');
            set({ user: null, session: null, loading: false, isLoading: false });
          }
        } catch (error) {
          console.error('🔍 AUTH_STORE: Exception during initialization:', error);
          set({ user: null, session: null, loading: false, isLoading: false });
        }
      },
    }),
    {
      name: 'playbeg-auth-store',
      storage: createJSONStorage(() => localStorage),
      // Only persist essential auth state
      partialize: (state) => ({
        user: state.user,
        session: state.session,
        needsOnboarding: state.needsOnboarding,
        // Don't persist loading states
      }),
      onRehydrateStorage: () => (state) => {
        console.log('🔍 AUTH_STORE: onRehydrateStorage called');
        if (state) {
          // Reset loading states on rehydration
          state.loading = false;
          state.isLoading = false;

          console.log('🔍 AUTH_STORE: Rehydrated from localStorage:', {
            hasUser: !!state.user,
            hasSession: !!state.session,
            needsOnboarding: state.needsOnboarding,
            userId: state.user?.id,
            sessionId: state.session?.access_token ? 'present' : 'missing'
          });
        } else {
          console.log('🔍 AUTH_STORE: No state to rehydrate');
        }
      },
    }
  )
);

// Helper function to check onboarding status
async function checkOnboardingStatus(userId: string) {
  try {
    const { data, error } = await supabase
      .from('dj_profiles')
      .select('completed_onboarding')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error checking onboarding status:', error);
      return;
    }

    const needsOnboarding = !data?.completed_onboarding;
    useAuthStore.getState().setNeedsOnboarding(needsOnboarding);
    
    console.log('🔍 AUTH_STORE: Onboarding status checked:', { needsOnboarding });
  } catch (error) {
    console.error('Exception checking onboarding status:', error);
  }
}

// Set up Supabase auth listener
let authListenerInitialized = false;
let authSubscription: any = null;

export function initializeAuthListener() {
  if (authListenerInitialized) return authSubscription;
  authListenerInitialized = true;

  console.log('🔍 AUTH_STORE: Setting up Supabase auth listener');

  const {
    data: { subscription },
  } = supabase.auth.onAuthStateChange(async (event, session) => {
    console.log('🔍 AUTH_STORE: Auth state change event:', event);
    console.log('🔍 AUTH_STORE: New session user:', session?.user?.id || 'null');

    const { setUser, setSession, setNeedsOnboarding } = useAuthStore.getState();

    if (session?.user) {
      setUser(session.user);
      setSession(session);
      
      // Check onboarding status only for SIGNED_IN events to avoid excessive calls
      if (event === 'SIGNED_IN') {
        await checkOnboardingStatus(session.user.id);
      }
      
      // Update last login timestamp for SIGNED_IN events only
      if (event === 'SIGNED_IN') {
        console.log('✅ AUTH_STORE: Updating last login timestamp');
        try {
          await supabase.rpc('update_last_login_timestamp', {
            user_id: session.user.id
          });
        } catch (loginTrackingError) {
          console.warn('Failed to update last login timestamp:', loginTrackingError);
        }
      }
      
      // Note: Realtime authentication will be handled by individual components
    } else {
      setUser(null);
      setSession(null);
      setNeedsOnboarding(false);
      
      // Note: Realtime disconnection will be handled by individual components
    }
  });

  authSubscription = subscription;
  return subscription;
}

// Compatibility hook for existing components using useAuth()
export function useAuth() {
  const user = useAuthStore(state => state.user);
  const loading = useAuthStore(state => state.loading);
  const isLoading = useAuthStore(state => state.isLoading);
  const needsOnboarding = useAuthStore(state => state.needsOnboarding);
  const signIn = useAuthStore(state => state.signIn);
  const signUp = useAuthStore(state => state.signUp);
  const signOut = useAuthStore(state => state.signOut);
  const completeOnboarding = useAuthStore(state => state.completeOnboarding);

  return {
    user,
    loading,
    isLoading,
    needsOnboarding,
    signIn,
    signUp,
    signOut,
    completeOnboarding,
  };
}
