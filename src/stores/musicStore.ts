import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { MusicService, MusicConnectionState } from '@/services/MusicService';

interface MusicState {
  connectionState: MusicConnectionState;
  isAuthorized: boolean;
  userToken: string | null;
  isInitializing: boolean;
  error: Error | null;
  isLoading: boolean;
  lastUpdated: number; // Timestamp to force UI updates

  // Actions
  initialize: () => Promise<void>;
  authorize: () => Promise<void>;
  disconnect: () => Promise<void>;
  checkConnection: () => Promise<void>;
  clearError: () => void;
  forceUpdate: () => void; // Force UI to update
}

export const useMusicStore = create<MusicState>()(
  persist(
    (set, get) => ({
      connectionState: 'disconnected',
      isAuthorized: false,
      userToken: null,
      isInitializing: false,
      error: null,
      isLoading: false,
      lastUpdated: Date.now(),

  initialize: async () => {
    const musicService = MusicService.getInstance();

    if (musicService.isInitialized()) {
      return;
    }

    set({ isInitializing: true, error: null });

    try {
      await musicService.initialize();
      const isAuthorized = await musicService.isAuthorized();
      console.log('initialize: isAuthorized =', isAuthorized);

      set({
        connectionState: isAuthorized ? 'connected' : 'disconnected',
        isAuthorized: isAuthorized,
        userToken: musicService.getUserToken(),
        lastUpdated: Date.now() // Single state update instead of forceUpdate
      });
    } catch (error) {
      set({
        connectionState: 'error',
        error: error instanceof Error ? error : new Error('Failed to initialize music service'),
        lastUpdated: Date.now()
      });
    } finally {
      set({ isInitializing: false });
    }
  },

  authorize: async () => {
    const musicService = MusicService.getInstance();

    if (!musicService.isInitialized()) {
      throw new Error('Music service not initialized');
    }

    set({ connectionState: 'connecting', error: null, isLoading: true });

    try {
      const success = await musicService.authorize();
      console.log('authorize: success =', success);

      if (success) {
        set({
          connectionState: 'connected',
          isAuthorized: true,
          userToken: musicService.getUserToken(),
          lastUpdated: Date.now() // Single state update
        });
      } else {
        throw new Error('Failed to authorize with Apple Music');
      }
    } catch (error) {
      set({
        connectionState: 'error',
        error: error instanceof Error ? error : new Error('Failed to authorize with Apple Music'),
        lastUpdated: Date.now()
      });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  disconnect: async () => {
    const musicService = MusicService.getInstance();

    set({ isLoading: true, error: null });

    try {
      const success = await musicService.disconnect();
      console.log('disconnect: success =', success);

      if (success) {
        set({
          connectionState: 'disconnected',
          isAuthorized: false,
          userToken: null,
          lastUpdated: Date.now() // Single state update
        });
      } else {
        throw new Error('Failed to disconnect from Apple Music');
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error : new Error('Failed to disconnect from Apple Music'),
        lastUpdated: Date.now()
      });
      throw error;
    } finally {
      set({ isLoading: false });
    }
  },

  checkConnection: async () => {
    const musicService = MusicService.getInstance();
    const { userToken, connectionState, isLoading } = get();

    // Don't check if we're already checking or if we're not initialized
    if (connectionState === 'connecting' || isLoading || !musicService.isInitialized()) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      // Check if we're authorized
      const isAuthorized = await musicService.isAuthorized();
      console.log('checkConnection: isAuthorized =', isAuthorized);

      // If we have a token and we're authorized, we're still connected
      if (userToken && isAuthorized) {
        set({
          connectionState: 'connected',
          isAuthorized: true,
          lastUpdated: Date.now() // Single state update
        });
        return;
      }

      // Otherwise, update our state to match the service
      set({
        isAuthorized: isAuthorized,
        userToken: musicService.getUserToken(),
        connectionState: isAuthorized ? 'connected' : 'disconnected',
        lastUpdated: Date.now() // Single state update
      });
    } catch (error) {
      set({
        connectionState: 'error',
        error: error instanceof Error ? error : new Error('Failed to check connection'),
        lastUpdated: Date.now()
      });
    } finally {
      set({ isLoading: false });
    }
  },

  clearError: () => {
    set({ error: null });
  },

  forceUpdate: () => {
    set({ lastUpdated: Date.now() });
  }
}),
{
  name: 'playbeg-music-store', // localStorage key
  storage: createJSONStorage(() => localStorage),
  // Only persist essential connection state, not transient states
  partialize: (state) => ({
    connectionState: state.connectionState,
    isAuthorized: state.isAuthorized,
    userToken: state.userToken,
    // Don't persist: isInitializing, error, isLoading, lastUpdated
  }),
  // Rehydrate with safe defaults for non-persisted state
  onRehydrateStorage: () => (state) => {
    if (state) {
      // Reset transient states on rehydration
      state.isInitializing = false;
      state.error = null;
      state.isLoading = false;
      state.lastUpdated = Date.now();

      console.log('🎵 MUSIC_STORE: Rehydrated from localStorage:', {
        connectionState: state.connectionState,
        isAuthorized: state.isAuthorized,
        hasUserToken: !!state.userToken
      });
    }
  },
}
)
);