import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/stores/authStore';
import { useMusicStore } from '@/stores/musicStore';
import DashboardTabs from '@/components/dashboard/DashboardTabs';
import { LastLoginInfo } from '@/components/dashboard/LastLoginInfo';
import ConnectionStatus from '@/components/apple-music/ConnectionStatus';
import { useDashboardData } from '@/hooks/useDashboardData';
import { Loader2 } from 'lucide-react';
import { useAppleMusicAuth } from '@/hooks/useAppleMusicAuth';
import { supabase } from '@/integrations/supabase/client';
import { DJProfile } from '@/lib/types';
import { RealtimeConnectionStatus } from '@/components/debug/RealtimeConnectionStatus';
import { useStrictModeEffect, useOnceEffect, useUserEffect } from '@/hooks/useStrictModeEffect';
import { appleMusicInitManager } from '@/utils/appleMusicInitManager';

let renderCount = 0;
let lastUserId: string | undefined = undefined;
let lastRenderCause: string = 'initial';

export default function Dashboard() {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();

  // Reset render count when user changes (new login)
  if (user?.id !== lastUserId) {
    renderCount = 0;
    lastUserId = user?.id;
    lastRenderCause = 'user-change';
  }

  renderCount++;

  if (renderCount > 3) {
    console.warn(`⚠️ DASHBOARD: EXCESSIVE RENDERS! Count: ${renderCount}`);
  }

  // CRITICAL FIX: Only subscribe to specific music store values to prevent re-renders
  // The useMusicStore was causing re-renders every time ANY music store state changed
  const isAuthorized = useMusicStore(state => state.isAuthorized);
  const checkConnection = useMusicStore(state => state.checkConnection);

  const { sessions: rawSessions, subscriptions, loading: dataLoading, error, fetchAllData } = useDashboardData(user?.id);

  // Enhanced render tracking to identify exact causes - moved after variable declarations
  const renderTracker = React.useMemo(() => {
    const tracker = {
      userId: user?.id,
      authLoading,
      isAuthorized,
      dataLoading,
      rawSessionsLength: rawSessions?.length,
      subscriptionsLength: subscriptions?.length,
      error: !!error,
      renderTime: Date.now()
    };

    // Only log detailed info for first few renders or when excessive
    if (renderCount <= 3 || renderCount % 5 === 0) {
      console.log(`🔍 DASHBOARD: Render #${renderCount} state:`, {
        userId: tracker.userId || 'none',
        authLoading: tracker.authLoading,
        isAuthorized: tracker.isAuthorized,
        dataLoading: tracker.dataLoading,
        sessionsCount: tracker.rawSessionsLength || 0,
        subscriptionsCount: tracker.subscriptionsLength || 0,
        hasError: tracker.error
      });
    }

    return tracker;
  }, [user?.id, authLoading, isAuthorized, dataLoading, rawSessions?.length, subscriptions?.length, error]);

  // Memoize sessions array to prevent unnecessary re-renders when reference changes but content is the same
  const sessions = useMemo(() => rawSessions, [rawSessions]);

  // Memoize fetchAllData to prevent it from changing on every render
  const stableFetchAllData = useCallback(() => {
    if (fetchAllData) {
      fetchAllData();
    }
  }, [fetchAllData]);
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'sessions');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(searchParams.get('session'));
  const [viewMode, setViewMode] = useState<string | null>(searchParams.get('view'));
  const [userProfile, setUserProfile] = useState<DJProfile | null>(null);

  // Memoize callback functions to prevent unnecessary re-renders
  const handleTabChange = useCallback((tab: string) => {
    setActiveTab(tab);
    const newParams = new URLSearchParams(searchParams);
    newParams.set('tab', tab);
    if (tab !== 'sessions' && selectedSessionId) {
      newParams.delete('session');
      setSelectedSessionId(null);
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams, selectedSessionId]);

  const handleSessionChange = useCallback((sessionId: string | null) => {
    setSelectedSessionId(sessionId);
    const newParams = new URLSearchParams(searchParams);
    if (sessionId) {
      newParams.set('session', sessionId);
    } else {
      newParams.delete('session');
      newParams.delete('view'); // Clear view when going back to sessions list
      setViewMode(null);
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  const handleViewModeChange = useCallback((view: string | null) => {
    setViewMode(view);
    const newParams = new URLSearchParams(searchParams);
    if (view) {
      newParams.set('view', view);
    } else {
      newParams.delete('view');
    }
    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);
  const FETCH_COOLDOWN = 30 * 1000; // 30 seconds cooldown for fetchAllData
  const CHECK_COOLDOWN = 5000; // 5 seconds cooldown for connection checks

  // CRITICAL FIX: Only extract syncConnectionState to prevent re-renders from other hook state changes
  const appleMusicAuth = useAppleMusicAuth();
  const syncConnectionState = useMemo(() => appleMusicAuth.syncConnectionState, [appleMusicAuth.syncConnectionState]);

  const hasInitialized = useRef(false);

  // Component mount/unmount tracking with StrictMode safety and comprehensive cleanup
  useOnceEffect(() => {
    console.log('✅ DASHBOARD: Component mounted (StrictMode-safe)');

    return () => {
      console.log('✅ DASHBOARD: Component cleanup (StrictMode-safe)');

      // Only reset Apple Music state on real unmount, not StrictMode cleanup
      // The appleMusicInitManager now has built-in StrictMode protection
      if (user?.id) {
        appleMusicInitManager.resetUser(user.id); // This will be protected from StrictMode resets
      }

      // Clear any pending timeouts or intervals
      // (Individual effects handle their own cleanup, but this is a safety net)
    };
  }, 'dashboard-mount');

  // Remove this effect - ProtectedRoute should handle authentication redirects
  // useEffect(() => {
  //   if (!authLoading && !user) {
  //     navigate('/login');
  //   }
  // }, [user, authLoading, navigate]);

  // Initialize Apple Music connection - once per user, StrictMode safe
  useUserEffect(() => {
    if (!authLoading && syncConnectionState && user?.id) {
      console.log('✅ DASHBOARD: Checking Apple Music initialization for user:', user.id);

      // Check if already initialized or initializing
      if (appleMusicInitManager.isUserInitialized(user.id)) {
        console.log('✅ DASHBOARD: Apple Music already initialized for user:', user.id);
        return;
      }

      if (appleMusicInitManager.isUserInitializing(user.id)) {
        console.log('✅ DASHBOARD: Apple Music already initializing for user:', user.id);
        return;
      }

      // Add a small delay to ensure component is stable
      const timeoutId = setTimeout(async () => {
        try {
          await appleMusicInitManager.initializeForUser(user.id!, syncConnectionState);
        } catch (error) {
          console.error('Error initializing Apple Music:', error);
        }
      }, 300); // Longer delay for stability

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, user?.id, 'apple-music-init');

  // Fetch user profile when user changes (separate from data fetching)
  useEffect(() => {
    if (user && !authLoading) {
      console.log('✅ DASHBOARD: Fetching user profile');

      // Set initial fetch time to prevent immediate refresh on visibility change
      setLastFetchTime(Date.now());

      // Fetch user profile to get display name
      const fetchUserProfile = async () => {
        try {
          const { data, error } = await supabase
            .from('dj_profiles')
            .select('*')
            .eq('id', user.id)
            .maybeSingle();

          if (error) throw error;

          if (data) {
            setUserProfile(data as DJProfile);
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      };

      fetchUserProfile();
    }
  }, [user?.id, authLoading]); // No fetchAllData dependency - let useDashboardData handle its own fetching

  // Visibility change handler with comprehensive cleanup
  useStrictModeEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const now = Date.now();

        // Always check connection with shorter cooldown
        if (now - lastFetchTime >= CHECK_COOLDOWN) {
          if (checkConnection) {
            checkConnection();
          }
          // Only sync connection state if we haven't initialized yet
          if (!hasInitialized.current && syncConnectionState) {
            syncConnectionState();
            hasInitialized.current = true;
          }
        }

        // Only refresh dashboard data after longer cooldown to prevent
        // unnecessary remounting when opening display windows
        if (now - lastFetchTime >= FETCH_COOLDOWN) {
          console.log('🔍 DASHBOARD: Refreshing data after', (now - lastFetchTime) / 1000, 'seconds away');
          stableFetchAllData();
          setLastFetchTime(now);
        } else {
          console.log('🔍 DASHBOARD: Skipping data refresh, only', (now - lastFetchTime) / 1000, 'seconds since last fetch');
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [lastFetchTime], { debugName: 'visibility-handler' }); // Keep minimal dependencies to prevent render loops

  // Use state for isDashboardReady to prevent it from flipping back to false
  const [isDashboardReady, setIsDashboardReady] = useState(false);
  const readyCheckRef = useRef(false);

  useEffect(() => {
    // Only check once to prevent re-render loops
    if (!readyCheckRef.current && !authLoading && !dataLoading && user && sessions !== undefined) {
      readyCheckRef.current = true;
      console.log('✅ DASHBOARD: Setting isDashboardReady to true (one-time check)');
      setIsDashboardReady(true);
    }
  }, [authLoading, dataLoading, user, sessions]);

  if (!isDashboardReady) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Loading Dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">Error loading dashboard data: {error.message}</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto px-0 md:px-4 py-0 md:py-8">
      {/* Mobile Header - Only visible on mobile */}
      <div className="flex items-center gap-4 px-4 py-3 md:hidden">
        <div className="w-10 h-10 bg-purple-600 text-white text-xl font-bold flex items-center justify-center rounded-full overflow-hidden flex-shrink-0">
          {userProfile?.profile_image_url ? (
            <img
              src={userProfile.profile_image_url}
              alt={userProfile?.display_name || "DJ"}
              className="w-full h-full object-cover"
            />
          ) : (
            <span>{userProfile?.display_name?.charAt(0) || "D"}</span>
          )}
        </div>
        <div className="flex flex-col">
          <h1 className="text-xl font-bold">DJ Dashboard</h1>
          <div className="flex flex-col">
            <h2 className="text-sm font-medium">
              Welcome{userProfile?.display_name ? `, ${userProfile.display_name}` : ""}
            </h2>
            <p className="text-xs text-muted-foreground">Manage your sessions and playlists.</p>
            <LastLoginInfo lastLoginAt={userProfile?.last_login_at} className="mt-1" />
          </div>
        </div>
      </div>

      {/* Welcome Section - Only visible on desktop */}
      <div className="hidden md:block px-4 mb-8">
        <h1 className="text-3xl font-bold mb-2">
          Welcome{userProfile?.display_name ? `, ${userProfile.display_name}` : ""}
        </h1>
        <p className="text-muted-foreground">Manage your sessions and playlists.</p>
        <LastLoginInfo lastLoginAt={userProfile?.last_login_at} className="mt-2" />
      </div>

      <div className="px-4 mb-3 md:mb-6">
        <ConnectionStatus />
      </div>

      <DashboardTabs
        activeTab={activeTab}
        onTabChange={handleTabChange}
        isAppleMusicConnected={isAuthorized}
        selectedSessionId={selectedSessionId}
        onSelectSession={handleSessionChange}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        sessions={sessions}
        loading={dataLoading}
        onSessionCreated={stableFetchAllData}
        onSessionDeleted={stableFetchAllData}
      />

      {/* Debug component - only shows in development */}
      <RealtimeConnectionStatus />
    </div>
  );
}
