import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "@/stores/authStore";
import { motion } from "framer-motion";

const ProtectedRoute = () => {
  const { user, isLoading, needsOnboarding } = useAuth();
  const location = useLocation();
  const isOnboardingRoute = location.pathname === '/onboarding';

  console.log('🔍 PROTECTED_ROUTE: Checking auth state:', {
    user: user?.id || 'null',
    isLoading,
    needsOnboarding,
    pathname: location.pathname
  });

  // Show loading state while auth is being checked
  if (isLoading) {
    console.log('🔍 PROTECTED_ROUTE: Showing loading state');
    return (
      <div className="flex h-screen items-center justify-center bg-black">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center"
        >
          <motion.div 
            animate={{ 
              rotate: 360,
              borderRadius: ["50% 50% 50% 50%", "30% 70% 70% 30%", "50% 50% 50% 50%"]
            }}
            transition={{ 
              repeat: Infinity,
              duration: 2,
              ease: "easeInOut"
            }}
            className="h-16 w-16 border-4 border-transparent border-t-purple-500 border-r-cyan-500"
          />
          <motion.p 
            initial={{ opacity: 0 }} 
            animate={{ opacity: 1 }} 
            transition={{ delay: 0.5 }}
            className="mt-4 text-gray-400"
          >
            Loading...
          </motion.p>
        </motion.div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    console.log('🔍 PROTECTED_ROUTE: No user found, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  // If user needs onboarding and is not already on the onboarding page, redirect to onboarding
  if (needsOnboarding && !isOnboardingRoute) {
    return <Navigate to="/onboarding" replace />;
  }

  // If user has completed onboarding and tries to access the onboarding page, redirect to dashboard
  if (!needsOnboarding && isOnboardingRoute) {
    return <Navigate to="/dashboard" replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
