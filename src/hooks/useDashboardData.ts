import { useState, useCallback, useRef, useEffect, useMemo } from "react";
import { supabase } from '@/integrations/supabase/client';
import { SessionWithSettings } from '@/types/session';
import { realtimeManager } from '@/utils/realtimeConnectionManager';

interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
  created_at: string;
  updated_at: string;
}

export function useDashboardData(userId?: string) {
  const [sessions, setSessions] = useState<SessionWithSettings[]>([]);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const abortControllerRef = useRef<AbortController>();
  const subscriptionsRef = useRef<any[]>([]);
  const hasInitializedRef = useRef(false); // Track if we've done initial load
  const isLoadingRef = useRef(false); // Prevent concurrent loading

  // Only log once when userId changes
  const hasLoggedRef = useRef<string | undefined>();
  if (hasLoggedRef.current !== userId && process.env.NODE_ENV === 'development') {
    console.log('✅ DASHBOARD_DATA: Hook initialized with userId:', userId);
    hasLoggedRef.current = userId;
    hasInitializedRef.current = false; // Reset initialization flag when user changes
  }

  const fetchSessionsOnly = useCallback(async () => {
    if (!userId) return;

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    setLoading(true);
    setError(null);

    try {
      const sessionsResult = await supabase
        .from('sessions')
        .select('*')
        .eq('dj_id', userId)
        .order('created_at', { ascending: false });

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;

      // Update sessions state only
      setSessions(sessionsResult.data || []);
    } catch (err) {
      console.error('Failed to fetch sessions:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch sessions'));
    } finally {
      setLoading(false);
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  const fetchAllData = useCallback(async () => {
    if (!userId) return;

    // Prevent concurrent loading
    if (isLoadingRef.current) {
      console.log('✅ DASHBOARD_DATA: Skipping concurrent fetch for userId:', userId);
      return;
    }

    console.log('✅ DASHBOARD_DATA: fetchAllData called for userId:', userId);

    // Cancel any in-progress fetches
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller for this fetch
    abortControllerRef.current = new AbortController();

    isLoadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      // Fetch sessions and subscriptions in parallel
      const [sessionsResult, subscriptionsResult] = await Promise.all([
        supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', userId)
          .order('created_at', { ascending: false }),
        supabase
          .from('dj_subscriptions')
          .select('*')
          .eq('dj_id', userId)
      ]);

      // Handle any errors
      if (sessionsResult.error) throw sessionsResult.error;
      if (subscriptionsResult.error) throw subscriptionsResult.error;

      // Update state with new data
      setSessions(sessionsResult.data || []);
      setSubscriptions(subscriptionsResult.data || []);
      hasInitializedRef.current = true;
    } catch (err) {
      console.error('Failed to fetch dashboard data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      setLoading(false);
      isLoadingRef.current = false;
      abortControllerRef.current = undefined;
    }
  }, [userId]);

  // Fetch data and set up subscriptions when userId changes
  useEffect(() => {
    if (!userId) return;

    console.log('✅ DASHBOARD_DATA: Setting up data fetch and subscriptions for userId:', userId);

    // Create a stable reference to fetchAllData for this effect
    const currentFetchAllData = fetchAllData;
    let isActive = true; // Flag to prevent operations after cleanup

    // Only fetch initial data if we haven't initialized yet
    // This prevents the loading state from flipping from false to true
    if (!hasInitializedRef.current) {
      currentFetchAllData();
    }

    // Set up realtime subscriptions using the centralized manager
    const unsubscribeFunctions: (() => void)[] = [];

    // Subscribe to session changes
    const unsubscribeSessionChanges = realtimeManager.subscribe({
      channelName: `dashboard-session-changes-${userId}`,
      table: 'sessions',
      event: '*',
      filter: `dj_id=eq.${userId}`,
      callback: (payload) => {
        if (isActive) {
          console.log('✅ Session change detected, refreshing data');
          currentFetchAllData();
        }
      }
    });

    // Subscribe to subscription changes
    const unsubscribeSubscriptionChanges = realtimeManager.subscribe({
      channelName: `dashboard-subscription-changes-${userId}`,
      table: 'dj_subscriptions',
      event: '*',
      filter: `dj_id=eq.${userId}`,
      callback: (payload) => {
        if (isActive) {
          console.log('✅ Subscription change detected, refreshing data');
          currentFetchAllData();
        }
      }
    });

    unsubscribeFunctions.push(unsubscribeSessionChanges, unsubscribeSubscriptionChanges);

    return () => {
      console.log('✅ DASHBOARD_DATA: Cleaning up subscriptions for userId:', userId);
      isActive = false;
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [userId]); // FIXED: Remove fetchAllData dependency to break circular dependency

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    sessions,
    subscriptions,
    loading,
    error,
    fetchAllData,
    fetchSessionsOnly,
  };
}
