import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/stores/authStore';
import { MusicService } from '@/services/MusicService';
import { debounce } from 'lodash';
import { useMusicStore } from '@/stores/musicStore';

export type AppleMusicConnectionState =
  | 'checking'
  | 'connected'
  | 'disconnected'
  | 'expired'
  | 'error'
  | 'connecting';

export function useAppleMusicAuth() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [connectionState, setConnectionState] = useState<AppleMusicConnectionState>('disconnected');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const musicService = useMemo(() => MusicService.getInstance(), []);
  const initializationAttempts = useRef(0);
  const maxInitializationAttempts = 3;
  const lastRefreshTime = useRef(0);
  const REFRESH_COOLDOWN = 5000; // 5 seconds cooldown
  const hasRunSyncRef = useRef(false);

  // Get the music store state to ensure UI updates
  const musicStoreLastUpdated = useMusicStore(state => state.lastUpdated);
  const musicStoreConnectionState = useMusicStore(state => state.connectionState);
  const musicStoreIsAuthorized = useMusicStore(state => state.isAuthorized);

  // Set user ID in MusicService when available
  useEffect(() => {
    if (user?.id) {
      musicService.setUserId(user.id);
    }
  }, [user?.id, musicService]);

  // Update local state when music store state changes
  useEffect(() => {
    // This effect will run whenever musicStoreLastUpdated changes
    console.log('Music store updated, connection state:', musicStoreConnectionState, 'isAuthorized:', musicStoreIsAuthorized);

    if (musicStoreConnectionState === 'connected' && musicStoreIsAuthorized) {
      setConnectionState('connected');
    } else if (musicStoreConnectionState === 'disconnected') {
      setConnectionState('disconnected');
    } else if (musicStoreConnectionState === 'error') {
      setConnectionState('error');
    }
  }, [musicStoreLastUpdated, musicStoreConnectionState, musicStoreIsAuthorized]);

  // Initialize MusicKit only once when the component mounts
  const hasInitializedRef = useRef(false);
  useEffect(() => {
    if (hasInitializedRef.current) return;
    hasInitializedRef.current = true;
    console.log('✅ APPLE_MUSIC: Initializing once on mount (with guard)');

    const initializeMusicKit = async () => {
      if (!isInitialized && !isSyncing && initializationAttempts.current < maxInitializationAttempts) {
        try {
          setIsSyncing(true);
          setConnectionState('connecting');
          initializationAttempts.current += 1;

          console.log(`✅ APPLE_MUSIC: Initializing MusicKit (attempt ${initializationAttempts.current}/${maxInitializationAttempts})`);

          // Add a small delay before initialization to ensure any previous attempts have settled
          if (initializationAttempts.current > 1) {
            // Increase delay for subsequent attempts
            const delayTime = initializationAttempts.current * 1000;
            console.log(`Waiting ${delayTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delayTime));
          }

          // Check network connectivity before attempting to initialize
          if (!navigator.onLine) {
            console.warn('Device appears to be offline. MusicKit initialization may fail.');
            // We'll still try to initialize, but with a warning
          }

          const success = await musicService.initialize();

          if (success) {
            console.log('MusicKit initialized successfully');
            setIsInitialized(true);
            setConnectionState('disconnected');

            // Add a small delay before syncing to ensure MusicKit is fully ready
            await new Promise(resolve => setTimeout(resolve, 500));

            // If we have a user, try to sync the connection state
            if (user) {
              hasRunSyncRef.current = true;
              await syncConnectionState();
            }
          } else {
            console.error('Failed to initialize MusicKit');
            setError('Failed to initialize MusicKit');
            setConnectionState('error');

            if (initializationAttempts.current < maxInitializationAttempts) {
              // Wait before retrying
              await new Promise(resolve => setTimeout(resolve, 2000));
              initializeMusicKit();
            } else {
              toast({
                title: "Initialization Failed",
                description: "Failed to initialize Apple Music after multiple attempts. Please try again later.",
                variant: "destructive",
              });
            }
          }
        } catch (error) {
          console.error('Error initializing MusicKit:', error);
          setError(error instanceof Error ? error.message : 'Failed to initialize MusicKit');
          setConnectionState('error');

          // Provide more user-friendly error message
          let errorMessage = 'Failed to initialize Apple Music';
          let showToast = true;

          if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            errorMessage = 'Apple Music services are currently unavailable. The app will continue to function with limited music features.';
          } else if (error instanceof Error) {
            errorMessage = `Apple Music error: ${error.message}`;
          }

          if (initializationAttempts.current < maxInitializationAttempts) {
            // Wait before retrying with exponential backoff
            const waitTime = Math.pow(2, initializationAttempts.current) * 1000; // 2s, 4s, 8s
            console.log(`Will retry in ${waitTime}ms (attempt ${initializationAttempts.current}/${maxInitializationAttempts})`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
            showToast = false; // Don't show toast for intermediate attempts
            initializeMusicKit();
          }

          // Show toast only after all retries or if explicitly set
          if (showToast && initializationAttempts.current >= maxInitializationAttempts) {
            toast({
              title: "Apple Music Limited Functionality",
              description: errorMessage,
              variant: "warning",
              duration: 7000, // Show for longer (7 seconds)
            });
          }
        } finally {
          setIsSyncing(false);
        }
      }
    };

    initializeMusicKit();
  }, []); // Only run once on mount, don't depend on user changes

  // Sync connection state when user changes, but only once
  useEffect(() => {
    if (user && isInitialized && !isSyncing && !hasRunSyncRef.current) {
      console.log('✅ APPLE_MUSIC: Syncing connection state once for user');
      // Add a short delay to prevent immediate execution
      const timer = setTimeout(() => {
        syncConnectionState();
        hasRunSyncRef.current = true;
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [user?.id, isInitialized, isSyncing]); // Use user.id instead of user object to prevent unnecessary re-runs

  const syncConnectionState = useCallback(async () => {
    // Always return a promise to ensure .catch() can be called
    if (!user || isSyncing) return Promise.resolve(false);

    try {
      setIsSyncing(true);
      setConnectionState('connecting');
      console.log('Syncing Apple Music connection state for user:', user.id);

      // Get the music store actions
      const { checkConnection } = useMusicStore.getState();

      // First, ensure MusicKit is initialized
      if (!musicService.isInitialized()) {
        console.log('MusicKit not initialized, initializing before syncing connection state');
        const initialized = await musicService.initialize();
        if (!initialized) {
          console.error('Failed to initialize MusicKit during connection sync');
          throw new Error('MusicKit not initialized');
        }
        setIsInitialized(true);
      }

      // Check if already authorized first (fastest path)
      if (await musicService.isAuthorized()) {
        console.log('Already authorized with MusicKit');
        setConnectionState('connected');

        // Also update the music store
        await checkConnection();

        return true;
      }

      // Check for token in database
      const { data, error } = await supabase
        .from('apple_music_tokens')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_valid', true)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      if (data && data.length > 0) {
        console.log('Valid Apple Music token found in database');
        const tokenData = data[0];

        // Check if token is expired
        const expiresAt = new Date(tokenData.expires_at);
        const now = new Date();
        const isValid = expiresAt > now;

        console.log('Database token valid:', isValid);

        if (isValid) {
          // Try to set the token and authorize
          console.log('Setting music user token and authorizing...');

          try {
            const success = await musicService.authorize();
            if (success) {
              console.log('Successfully authorized with MusicKit');
              setConnectionState('connected');

              // Also update the music store
              await checkConnection();

              return true;
            }
          } catch (authError) {
            console.error('Error during authorization with stored token:', authError);
            // Continue to try other methods
          }
        }
      }

      // Default to disconnected if no valid token found
      setConnectionState('disconnected');

      // Also update the music store
      await checkConnection();

      return false;
    } catch (error) {
      console.error('Error syncing Apple Music connection:', error);
      setConnectionState('error');
      setError(error instanceof Error ? error.message : 'Failed to sync Apple Music connection');

      // Update the music store with the error
      const { forceUpdate } = useMusicStore.getState();
      forceUpdate();

      return false;
    } finally {
      setIsSyncing(false);
    }
  }, [user, isSyncing, musicService, useMusicStore]);

  // Debounced refresh function with cooldown
  const refreshConnection = useCallback(
    debounce(async () => {
      const now = Date.now();
      if (!isSyncing && now - lastRefreshTime.current >= REFRESH_COOLDOWN) {
        console.log("🔄 Manually refreshing Apple Music connection");
        lastRefreshTime.current = now;
        await syncConnectionState();
      }
    }, 1000),
    [syncConnectionState, isSyncing]
  );

  const handleConnect = async () => {
    console.log('useAppleMusicAuth: handleConnect called');
    if (!user) {
      console.log('useAppleMusicAuth: No user found, cannot connect');
      toast({
        title: "Authentication Required",
        description: "Please sign in to connect your Apple Music account.",
        variant: "destructive",
      });
      return false;
    }

    try {
      console.log('useAppleMusicAuth: Setting connection state to connecting');
      setConnectionState('connecting');
      console.log('useAppleMusicAuth: Calling musicService.authorize()');
      const success = await musicService.authorize();
      console.log('useAppleMusicAuth: Authorization result:', success);

      if (success) {
        console.log('useAppleMusicAuth: Setting connection state to connected');
        setConnectionState('connected');
        toast({
          title: "Success",
          description: "Successfully connected to Apple Music",
        });
        return true;
      } else {
        console.log('useAppleMusicAuth: Setting connection state to disconnected');
        setConnectionState('disconnected');
        toast({
          title: "Connection Failed",
          description: "Failed to connect to Apple Music. Please try again.",
          variant: "destructive",
        });
        return false;
      }
    } catch (error) {
      console.error('useAppleMusicAuth: Error connecting to Apple Music:', error);
      setConnectionState('error');
      setError(error instanceof Error ? error.message : 'Failed to connect to Apple Music');
      toast({
        title: "Connection Error",
        description: error instanceof Error ? error.message : 'Failed to connect to Apple Music',
        variant: "destructive",
      });
      return false;
    }
  };

  const handleDisconnect = async () => {
    try {
      setConnectionState('disconnected');
      await musicService.disconnect();
      toast({
        title: "Disconnected",
        description: "Successfully disconnected from Apple Music",
      });
      return true;
    } catch (error) {
      console.error('Error disconnecting from Apple Music:', error);
      setError(error instanceof Error ? error.message : 'Failed to disconnect from Apple Music');
      toast({
        title: "Disconnection Error",
        description: error instanceof Error ? error.message : 'Failed to disconnect from Apple Music',
        variant: "destructive",
      });
      return false;
    }
  };

  // Define if user is authorized based on connection state
  const isAuthorized = connectionState === 'connected';

  return {
    connectionState,
    isAuthorized,
    error,
    connectAppleMusic: handleConnect,
    syncConnectionState: refreshConnection,
    disconnectAppleMusic: handleDisconnect,
  };
}
