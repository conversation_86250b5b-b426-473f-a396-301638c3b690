import { useEffect, useRef } from 'react';
import { realtimeManager } from '@/utils/realtimeConnectionManager';

interface UseRealtimeSubscriptionOptions {
  channelName: string;
  table: string;
  event?: string;
  filter?: string;
  callback: (payload: any) => void;
  enabled?: boolean;
}

export function useRealtimeSubscription({
  channelName,
  table,
  event = '*',
  filter,
  callback,
  enabled = true
}: UseRealtimeSubscriptionOptions) {
  const callbackRef = useRef(callback);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    if (!enabled) {
      // Clean up existing subscription if disabled
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
      return;
    }

    console.log('🔌 useRealtimeSubscription: Setting up subscription for:', channelName);

    // Set up subscription
    const unsubscribe = realtimeManager.subscribe({
      channelName,
      table,
      event,
      filter,
      callback: (payload) => callbackRef.current(payload)
    });

    unsubscribeRef.current = unsubscribe;

    return () => {
      console.log('🔌 useRealtimeSubscription: Cleaning up subscription for:', channelName);
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [channelName, table, event, filter, enabled]);

  return {
    isSubscribed: !!unsubscribeRef.current
  };
}
