import { useState, useEffect } from "react";
import { useAuth } from "@/stores/authStore";
import { supabase } from "@/integrations/supabase/client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/components/ui/use-toast";

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string | null;
  price_amount: number;
  price_currency: string;
  duration_hours: number;
  stripe_price_id: string;
}

export interface Subscription {
  id: string;
  dj_id: string;
  plan_id: string | null;
  status: string;
  stripe_customer_id: string | null;
  stripe_subscription_id: string | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean | null;
}

export const useSubscription = () => {
  const { user } = useAuth();
  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  console.log("🔍 useSubscription hook initialized, user:", user?.id || "no user");

  // Fetch available subscription plans
  const { data: plans = [], isLoading: isPlansLoading } = useQuery({
    queryKey: ['subscription-plans'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .order('price_amount', { ascending: true });

      if (error) {
        console.error('Error fetching subscription plans:', error);
        throw error;
      }

      return data as SubscriptionPlan[];
    },
  });

  // Fetch current user's subscription with shorter staleTime to ensure more frequent refreshes
  const { data: subscription, isLoading: isSubscriptionLoading } = useQuery({
    queryKey: ['subscription', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('dj_subscriptions')
        .select('*')
        .eq('dj_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching subscription:', error);
        throw error;
      }

      return data as Subscription;
    },
    enabled: !!user?.id,
  });

  // Combine loading states
  const isLoading = isPlansLoading || isSubscriptionLoading;

  // Set up a realtime subscription to refresh when subscription data changes
  useEffect(() => {
    if (!user?.id) return;
    
    console.log(`🔌 Setting up realtime subscription for user: ${user.id}`);
    
    // Listen for changes to the user's subscription
    const channel = supabase
      .channel('subscription_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'dj_subscriptions',
        filter: `dj_id=eq.${user.id}`,
      }, (payload) => {
        console.log("🔄 Received subscription change event:", payload);
        // Invalidate the subscription query when changes occur
        queryClient.invalidateQueries({ queryKey: ['subscription', user.id] });
        console.log("🔄 Invalidated subscription query cache");
      })
      .subscribe();
    
    return () => {
      console.log("🔌 Removing subscription channel");
      supabase.removeChannel(channel);
    };
  }, [user?.id, queryClient]);

  // Get free plan
  const freePlan = plans?.find(plan => plan.name === 'Free' && plan.price_amount === 0);
  console.log("📊 Free plan:", freePlan ? JSON.stringify(freePlan, null, 2) : "No free plan found");

  // Get current plan details - prioritize paid plans
  const currentPlan = subscription && subscription.plan_id && plans
    ? plans.find(plan => plan.id === subscription.plan_id)
    : null;

  console.log("📊 Current plan:", currentPlan ? JSON.stringify(currentPlan, null, 2) : "No current plan");

  // Check if subscription has expired
  const isSubscriptionExpired = (): boolean => {
    if (!subscription || subscription.status !== 'active') return true;
    if (!subscription.current_period_end) return false; // If no end date, consider as not expired (like free plan)
    
    const endDate = new Date(subscription.current_period_end);
    const now = new Date();
    
    return now >= endDate;
  };

  // Determine if user has active subscription - with null safety checks
  const hasActiveSubscription = subscription && 
    (subscription.status === 'active' || subscription.status === 'trialing') &&
    (!subscription.current_period_end || new Date(subscription.current_period_end) > new Date());

  console.log(`📊 Has active subscription: ${hasActiveSubscription}`, {
    status: subscription?.status,
    current_period_end: subscription?.current_period_end,
    is_expired: isSubscriptionExpired(),
    has_free_plan: freePlan !== undefined
  });

  // If no active paid subscription or subscription expired, consider free plan as current
  const effectiveCurrentPlan = hasActiveSubscription && !isSubscriptionExpired() && currentPlan && currentPlan.name !== 'Free'
    ? currentPlan
    : freePlan;

  console.log("📊 Effective current plan:", effectiveCurrentPlan ? JSON.stringify(effectiveCurrentPlan, null, 2) : "No effective plan");

  // Simple approach: latest purchase is just the current plan
  // In a real implementation, you might want to fetch the latest purchase from a purchases table
  const latestPurchase = currentPlan && currentPlan.name !== 'Free' ? currentPlan : null;

  // Create checkout session
  const createCheckoutSession = async (planId: string) => {
    console.log(`🛒 Creating checkout session for plan: ${planId}`);
    
    if (!user) {
      console.error("❌ Authentication error: No user logged in");
      toast({
        title: "Authentication Error",
        description: "You must be logged in to purchase a subscription.",
        variant: "destructive",
      });
      throw new Error("User not authenticated");
    }
    
    setIsCheckoutLoading(true);
    
    try {
      // Get the latest session token to ensure it's fresh
      console.log("🔑 Getting latest auth session");
      const { data: sessionData } = await supabase.auth.getSession();
      
      if (!sessionData.session) {
        console.error("❌ Session expired");
        toast({
          title: "Session Expired",
          description: "Your login session has expired. Please log in again.",
          variant: "destructive",
        });
        throw new Error("No active session");
      }
      
      // Get the access token
      const accessToken = sessionData.session.access_token;
      
      if (!accessToken) {
        console.error("❌ No access token found");
        toast({
          title: "Authentication Error",
          description: "Could not retrieve authentication token. Please log in again.",
          variant: "destructive",
        });
        throw new Error("No access token");
      }
      
      // Construct the success and cancel URLs
      const origin = window.location.origin;
      const successUrl = `${origin}/dashboard?checkout=success`;
      const cancelUrl = `${origin}/dashboard?checkout=canceled`;
      
      console.log("📋 Creating checkout session with:", { 
        planId, 
        userId: user.id,
        successUrl, 
        cancelUrl 
      });
      
      // Call the create-checkout-session edge function
      console.log("🔄 Invoking create-checkout-session edge function");
      const response = await supabase.functions.invoke('create-checkout-session', {
        body: { 
          planId,
          successUrl,
          cancelUrl
        },
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });
      
      console.log("📊 Edge function response:", JSON.stringify(response, null, 2));
      
      if (response.error) {
        console.error("❌ Edge function error response:", response.error);
        throw new Error(response.error.message || "Failed to create checkout session");
      }
      
      if (response.data?.error) {
        console.error("❌ Server error in response:", response.data.error);
        throw new Error(response.data.error || "Server error in checkout process");
      }
      
      // Redirect to Stripe Checkout
      if (response.data?.sessionUrl) {
        console.log("🔀 Redirecting to Stripe checkout:", response.data.sessionUrl);
        window.location.href = response.data.sessionUrl;
      } else {
        console.error("❌ Missing session URL in response:", response);
        throw new Error("No checkout URL returned");
      }
      
      return response.data;
    } catch (error) {
      console.error("❌ Error creating checkout session:", error);
      console.error(error.stack || "No stack trace available");
      
      // Provide more specific error messages
      let errorMessage = "Failed to create checkout session";
      if (error.message) {
        errorMessage = error.message;
      } else if (error.error_description) {
        errorMessage = error.error_description;
      }
      
      toast({
        title: "Checkout Error",
        description: errorMessage,
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsCheckoutLoading(false);
    }
  };

  return {
    plans,
    subscription,
    currentPlan: effectiveCurrentPlan,
    latestPurchase,
    hasActiveSubscription: hasActiveSubscription && !isSubscriptionExpired(),
    isSubscriptionExpired,
    isLoading,
    isCheckoutLoading,
    createCheckoutSession,
  };
};
