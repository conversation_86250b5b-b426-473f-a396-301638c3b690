import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { DJProfile } from '@/lib/types';
import { useAuth } from '@/stores/authStore';

export function useDJProfile() {
  const [profile, setProfile] = useState<DJProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    async function fetchProfile() {
      if (!user) {
        setProfile(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const { data, error: fetchError } = await supabase
          .from('dj_profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (fetchError) {
          throw fetchError;
        }

        setProfile(data);
      } catch (err: any) {
        console.error('Error fetching DJ profile:', err);
        setError(err.message || 'Failed to fetch profile');
      } finally {
        setIsLoading(false);
      }
    }

    fetchProfile();
  }, [user]);

  const refreshProfile = async () => {
    if (!user) return;

    try {
      setError(null);
      const { data, error: fetchError } = await supabase
        .from('dj_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      setProfile(data);
    } catch (err: any) {
      console.error('Error refreshing DJ profile:', err);
      setError(err.message || 'Failed to refresh profile');
    }
  };

  return {
    profile,
    isLoading,
    error,
    refreshProfile,
    lastLoginAt: profile?.last_login_at,
  };
}
