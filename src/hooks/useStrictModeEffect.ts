import { useEffect, useRef } from 'react';

/**
 * A hook that provides StrictMode-safe effects with proper cleanup handling
 * Prevents double execution issues in React StrictMode during development
 */
export function useStrictModeEffect(
  effect: () => void | (() => void),
  deps: React.DependencyList,
  options: {
    runOnce?: boolean; // If true, effect runs only once even in StrictMode
    debugName?: string; // For debugging purposes
  } = {}
) {
  const { runOnce = false, debugName = 'unknown' } = options;
  const hasRunRef = useRef(false);
  const cleanupRef = useRef<(() => void) | null>(null);
  const mountTimeRef = useRef<number>(0);

  useEffect(() => {
    const currentTime = Date.now();
    mountTimeRef.current = currentTime;

    // If runOnce is true and we've already run, skip
    if (runOnce && hasRunRef.current) {
      console.log(`🔍 STRICT_MODE: Skipping duplicate effect for ${debugName}`);
      return;
    }

    console.log(`🔍 STRICT_MODE: Running effect for ${debugName} at ${new Date(currentTime).toISOString()}`);

    // Mark as run if runOnce is enabled
    if (runOnce) {
      hasRunRef.current = true;
    }

    // Execute the effect
    const cleanup = effect();

    // Store cleanup function
    if (typeof cleanup === 'function') {
      cleanupRef.current = cleanup;
    }

    return () => {
      const unmountTime = Date.now();
      const duration = unmountTime - mountTimeRef.current;

      if (duration < 100) {
        console.log(`🔍 STRICT_MODE: Quick cleanup for ${debugName} after ${duration}ms (likely StrictMode)`);
      } else {
        console.log(`🔍 STRICT_MODE: Normal cleanup for ${debugName} after ${duration}ms`);
      }

      // Execute cleanup if available
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }

      // Reset hasRun flag on cleanup if this was a quick unmount (StrictMode)
      if (runOnce && duration < 100) {
        hasRunRef.current = false;
      }
    };
  }, deps);

  // Return a function to manually reset the "has run" flag
  return {
    reset: () => {
      hasRunRef.current = false;
      console.log(`🔍 STRICT_MODE: Reset flag for ${debugName}`);
    }
  };
}

/**
 * A hook for one-time initialization that's safe in StrictMode
 */
export function useOnceEffect(
  effect: () => void | (() => void),
  debugName: string = 'once-effect'
) {
  return useStrictModeEffect(effect, [], { runOnce: true, debugName });
}

/**
 * A hook for user-dependent effects that should run once per user
 */
export function useUserEffect(
  effect: () => void | (() => void),
  userId: string | undefined,
  debugName: string = 'user-effect'
) {
  const lastUserRef = useRef<string | undefined>();
  const shouldRun = userId !== lastUserRef.current;

  useEffect(() => {
    if (shouldRun && userId) {
      lastUserRef.current = userId;
      console.log(`🔍 USER_EFFECT: Running ${debugName} for user ${userId}`);
      
      const cleanup = effect();
      
      return () => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      };
    }
  }, [userId, shouldRun]);
}
