import { useEffect, useRef } from 'react';

/**
 * StrictMode Detection Utility
 * Provides centralized logic for detecting React StrictMode double-execution patterns
 */
class StrictModeDetector {
  private static instance: StrictModeDetector;
  private effectStates: Map<string, {
    hasRun: boolean;
    lastMountTime: number;
    lastUnmountTime: number;
    isInStrictModeCleanup: boolean;
  }> = new Map();

  static getInstance(): StrictModeDetector {
    if (!StrictModeDetector.instance) {
      StrictModeDetector.instance = new StrictModeDetector();
    }
    return StrictModeDetector.instance;
  }

  /**
   * Check if an effect should run based on StrictMode detection
   */
  shouldRunEffect(effectId: string, runOnce: boolean): boolean {
    const state = this.effectStates.get(effectId);
    const now = Date.now();

    if (!state) {
      // First time running this effect
      this.effectStates.set(effectId, {
        hasRun: false,
        lastMountTime: now,
        lastUnmountTime: 0,
        isInStrictModeCleanup: false
      });
      return true;
    }

    // If runOnce is false, always run
    if (!runOnce) {
      state.lastMountTime = now;
      return true;
    }

    // Check if we're in a StrictMode remount scenario
    const timeSinceLastUnmount = now - state.lastUnmountTime;
    const isLikelyStrictModeRemount = state.lastUnmountTime > 0 && timeSinceLastUnmount < 50;

    if (isLikelyStrictModeRemount && state.hasRun) {
      console.log(`🔍 STRICT_MODE: Detected StrictMode remount for ${effectId}, skipping execution`);
      state.lastMountTime = now;
      state.isInStrictModeCleanup = false;
      return false;
    }

    // If we haven't run yet, or it's been a while since last unmount, run the effect
    if (!state.hasRun || timeSinceLastUnmount > 1000) {
      state.lastMountTime = now;
      state.hasRun = true;
      state.isInStrictModeCleanup = false;
      return true;
    }

    return false;
  }

  /**
   * Handle effect cleanup and detect StrictMode patterns
   */
  handleEffectCleanup(effectId: string): boolean {
    const state = this.effectStates.get(effectId);
    if (!state) return false;

    const now = Date.now();
    const mountDuration = now - state.lastMountTime;
    const isQuickCleanup = mountDuration < 100;

    state.lastUnmountTime = now;
    state.isInStrictModeCleanup = isQuickCleanup;

    if (isQuickCleanup) {
      console.log(`🔍 STRICT_MODE: Quick cleanup detected for ${effectId} after ${mountDuration}ms (likely StrictMode)`);
      // Don't reset hasRun flag for StrictMode cleanup
      return true; // Indicates this is likely a StrictMode cleanup
    } else {
      console.log(`🔍 STRICT_MODE: Normal cleanup for ${effectId} after ${mountDuration}ms`);
      // Reset hasRun flag for normal cleanup
      state.hasRun = false;
      return false;
    }
  }

  /**
   * Reset state for a specific effect
   */
  resetEffect(effectId: string): void {
    const state = this.effectStates.get(effectId);
    if (state) {
      state.hasRun = false;
      state.isInStrictModeCleanup = false;
      console.log(`🔍 STRICT_MODE: Reset state for ${effectId}`);
    }
  }

  /**
   * Check if an effect is currently in StrictMode cleanup
   */
  isInStrictModeCleanup(effectId: string): boolean {
    const state = this.effectStates.get(effectId);
    return state?.isInStrictModeCleanup || false;
  }
}

const strictModeDetector = StrictModeDetector.getInstance();

/**
 * A hook that provides StrictMode-safe effects with proper cleanup handling
 * Prevents double execution issues in React StrictMode during development
 */
export function useStrictModeEffect(
  effect: () => void | (() => void),
  deps: React.DependencyList,
  options: {
    runOnce?: boolean; // If true, effect runs only once even in StrictMode
    debugName?: string; // For debugging purposes
  } = {}
) {
  const { runOnce = false, debugName = 'unknown' } = options;
  const cleanupRef = useRef<(() => void) | null>(null);
  const effectIdRef = useRef<string>(`${debugName}-${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    const effectId = effectIdRef.current;

    // Check if we should run this effect
    if (!strictModeDetector.shouldRunEffect(effectId, runOnce)) {
      return;
    }

    console.log(`🔍 STRICT_MODE: Running effect for ${debugName}`);

    // Execute the effect
    const cleanup = effect();

    // Store cleanup function
    if (typeof cleanup === 'function') {
      cleanupRef.current = cleanup;
    }

    return () => {
      const isStrictModeCleanup = strictModeDetector.handleEffectCleanup(effectId);

      // Execute cleanup if available
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, deps);

  // Return a function to manually reset the effect state
  return {
    reset: () => {
      strictModeDetector.resetEffect(effectIdRef.current);
    }
  };
}

/**
 * A hook for one-time initialization that's safe in StrictMode
 */
export function useOnceEffect(
  effect: () => void | (() => void),
  debugName: string = 'once-effect'
) {
  return useStrictModeEffect(effect, [], { runOnce: true, debugName });
}

/**
 * A hook for user-dependent effects that should run once per user
 * Uses the StrictMode detector to prevent duplicate execution
 */
export function useUserEffect(
  effect: () => void | (() => void),
  userId: string | undefined,
  debugName: string = 'user-effect'
) {
  const lastUserRef = useRef<string | undefined>();
  const effectIdRef = useRef<string>(`${debugName}-user-${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    if (!userId) return;

    const effectId = effectIdRef.current;
    const userChanged = userId !== lastUserRef.current;

    // If user changed, reset the effect state
    if (userChanged) {
      strictModeDetector.resetEffect(effectId);
      lastUserRef.current = userId;
    }

    // Check if we should run this effect (once per user)
    if (!strictModeDetector.shouldRunEffect(effectId, true)) {
      return;
    }

    console.log(`🔍 USER_EFFECT: Running ${debugName} for user ${userId}`);

    const cleanup = effect();

    return () => {
      const isStrictModeCleanup = strictModeDetector.handleEffectCleanup(effectId);

      if (typeof cleanup === 'function') {
        cleanup();
      }
    };
  }, [userId]);
}

/**
 * Export the StrictMode detector for use in other utilities
 */
export { strictModeDetector };
