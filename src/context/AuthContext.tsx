
import React, { createContext, useContext, useEffect, useState, useRef, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/components/ui/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { DJProfile } from "@/lib/types";
import { realtimeManager } from "@/utils/realtimeConnectionManager";

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isLoading: boolean; // Alias for loading to match ProtectedRoute
  needsOnboarding: boolean;
  completeOnboarding: () => Promise<void>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signUp: (email: string, password: string) => Promise<{ data: any; error: any }>;
  signOut: () => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [needsOnboarding, setNeedsOnboarding] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const location = useLocation();

  // Add a ref to track the latest user state to prevent race conditions
  const latestUserRef = useRef<User | null>(null);

  // Safe user state update function to prevent race conditions
  const updateUserState = (newUser: User | null, source: string) => {
    console.log(`🔍 AUTH: Updating user state from ${source}:`, newUser?.id || 'null');

    // Only update if this is actually a change
    if (latestUserRef.current?.id !== newUser?.id) {
      console.log(`🔍 AUTH: User state changed from ${latestUserRef.current?.id || 'null'} to ${newUser?.id || 'null'}`);
      latestUserRef.current = newUser;
      setUser(newUser);

      // Notify realtime manager about authentication status
      realtimeManager.setAuthenticationStatus(!!newUser);
    } else {
      console.log(`🔍 AUTH: User state unchanged, skipping update`);
    }
  };

  useEffect(() => {
    console.log('🔍 AUTH: Initializing AuthContext at', new Date().toISOString());

    let isMounted = true; // Flag to prevent state updates after unmount
    let initializationComplete = false; // Flag to prevent duplicate initialization

    const initializeAuth = async () => {
      if (initializationComplete) {
        console.log('🔍 AUTH: Skipping duplicate initialization');
        return;
      }

      try {
        console.log('🔍 AUTH: Starting session retrieval...');

        // Get initial session with error handling
        const { data: { session }, error } = await supabase.auth.getSession();

        console.log('🔍 AUTH: Session retrieval completed');
        console.log('🔍 AUTH: Session data:', session ? 'present' : 'null');
        console.log('🔍 AUTH: Session error:', error ? error.message : 'none');

        if (error) {
          console.error('🔍 AUTH: Error getting initial session:', error);
          if (isMounted) {
            updateUserState(null, 'initial-session-error');
            setLoading(false);
          }
          return;
        }

        console.log('🔍 AUTH: Initial session check at', new Date().toISOString());
        console.log('🔍 AUTH: Initial session user:', session?.user?.id || 'null');
        console.log('🔍 AUTH: Session access token present:', !!session?.access_token);
        console.log('🔍 AUTH: Window focus during initial session:', document.hasFocus());

        // Only update state if component is still mounted
        if (isMounted) {
          updateUserState(session?.user ?? null, 'initial-session');
          setLoading(false);
          initializationComplete = true;

          // Check if user needs onboarding
          if (session?.user) {
            checkOnboardingStatus(session.user.id);
          }
        }
      } catch (error) {
        console.error('🔍 AUTH: Exception during session initialization:', error);
        if (isMounted) {
          updateUserState(null, 'initial-session-exception');
          setLoading(false);
        }
      }
    };

    // Initialize auth state immediately
    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔍 AUTH: Auth state change event:', event, 'at', new Date().toISOString());
      console.log('🔍 AUTH: New session user:', session?.user?.id || 'null');
      console.log('🔍 AUTH: Window focus during auth change:', document.hasFocus());
      console.log('🔍 AUTH: Document visibility during auth change:', document.visibilityState);
      console.log('🔍 AUTH: Initialization complete:', initializationComplete);

      // Only update state if component is still mounted
      if (isMounted) {
        // For SIGNED_IN events during initialization, don't override if we already have a user
        if (event === 'SIGNED_IN' && !initializationComplete && latestUserRef.current) {
          console.log('🔍 AUTH: SIGNED_IN event during initialization, but user already set. Skipping update.');
          return;
        }

        updateUserState(session?.user ?? null, `auth-change-${event}`);
        setLoading(false);

        // Check if user needs onboarding
        if (session?.user) {
          checkOnboardingStatus(session.user.id);

          // Update last login timestamp for SIGNED_IN events (not for TOKEN_REFRESHED)
          if (event === 'SIGNED_IN') {
            console.log('✅ AUTH: Updating last login timestamp (single call from onAuthStateChange)');
            try {
              await supabase.rpc('update_last_login_timestamp', {
                user_id: session.user.id
              });
            } catch (loginTrackingError) {
              console.warn('Failed to update last login timestamp:', loginTrackingError);
            }
          }
        }
      }
    });

    return () => {
      isMounted = false; // Prevent state updates after cleanup
      subscription.unsubscribe();
    };
  }, []);

  // Helper function to validate session is still active
  const validateSession = async (session: Session | null): Promise<boolean> => {
    if (!session?.access_token) return false;

    try {
      // Try to make a simple authenticated request to verify the session
      const { error } = await supabase.auth.getUser();
      return !error;
    } catch {
      return false;
    }
  };

  // Function to check if user needs onboarding
  const checkOnboardingStatus = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('dj_profiles')
        .select('completed_onboarding')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking onboarding status:', error);
        return;
      }

      // If completed_onboarding is false or null, user needs onboarding
      setNeedsOnboarding(!data?.completed_onboarding);
    } catch (error) {
      console.error('Error checking onboarding status:', error);
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (data?.user) {
      // Don't update timestamp here - let onAuthStateChange handle it to avoid duplicates
      console.log('✅ AUTH: signIn() completed, no duplicate timestamp update');

      // Redirect to dashboard on successful login
      navigate('/dashboard');
    }

    return { data, error };
  };

  const signUp = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          // Add any metadata needed for the trigger function
          display_name: email.split('@')[0],
        }
      }
    });
    return { data, error };
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      // Redirect to home page on successful logout
      navigate('/');
    }
    return { error };
  };

  const completeOnboarding = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('dj_profiles')
        .update({ completed_onboarding: true })
        .eq('id', user.id);

      if (error) {
        console.error('Error completing onboarding:', error);
        toast({
          title: 'Error',
          description: 'Failed to complete onboarding. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      setNeedsOnboarding(false);
      toast({
        title: 'Onboarding Complete',
        description: 'Your profile has been set up successfully!'
      });

      // Redirect to dashboard
      navigate('/dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    }
  };

  const value = {
    user,
    loading,
    isLoading: loading, // Alias for loading to match ProtectedRoute
    needsOnboarding,
    completeOnboarding,
    signIn,
    signUp,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
