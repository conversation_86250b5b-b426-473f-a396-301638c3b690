import { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { Music2, X, LogIn, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/stores/authStore";
import { createPortal } from "react-dom";

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileMenu = ({ isOpen, onClose }: MobileMenuProps) => {
  const { user, signOut } = useAuth();
  const menuRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    // Focus trap
    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === "Tab" && isOpen && menuRef.current) {
        const focusableElements = menuRef.current.querySelectorAll(
          'a[href], button, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
          const firstElement = focusableElements[0] as HTMLElement;
          const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

          if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    // Set focus to the menu when it opens
    if (isOpen && menuRef.current) {
      const firstFocusable = menuRef.current.querySelector(
        'a[href], button, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement;

      if (firstFocusable) {
        setTimeout(() => firstFocusable.focus(), 100);
      }
    }

    window.addEventListener("keydown", handleEscKey);
    window.addEventListener("keydown", handleTabKey);
    setMounted(true);

    return () => {
      window.removeEventListener("keydown", handleEscKey);
      window.removeEventListener("keydown", handleTabKey);
    };
  }, [isOpen, onClose]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!mounted) return null;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-[999] md:hidden overflow-hidden isolate"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {/* Backdrop - 85% opacity black with medium blur for enhanced content dimming */}
          <motion.div
            className="absolute inset-0 bg-black/85 backdrop-blur-md z-[-1]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Menu Panel - Solid near-black background for complete content separation */}
          <motion.div
            ref={menuRef}
            className="absolute right-0 top-0 bottom-0 w-full max-w-xs bg-[#0d0d0d] flex flex-col z-[1000] shadow-xl border-l border-purple-500/20"
            role="navigation"
            aria-label="Mobile navigation"
            aria-modal="true"
            aria-expanded="true"
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", stiffness: 300, damping: 30, duration: 0.3 }}
          >
            {/* Header with Logo and Close Button */}
            <div className="flex items-center justify-between p-4 border-b border-purple-500/20">
              <Link
                to="/"
                className="flex items-center gap-2 text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent"
                onClick={onClose}
                tabIndex={0}
              >
                <Music2 className="h-5 w-5 sm:h-6 sm:w-6 text-purple-400" />
                PlayBeg
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                aria-label="Close menu"
                className="text-cyan-400 hover:text-white transition-colors"
                tabIndex={0}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation Links */}
            <div className="flex flex-col p-4 space-y-1">
              <Link
                to="/blog"
                className="w-full py-3 px-4 text-cyan-400 hover:text-white transition-colors duration-200 flex items-center"
                onClick={onClose}
                tabIndex={0}
              >
                Blog
              </Link>
              <Link
                to="/partner"
                className="w-full py-3 px-4 text-cyan-400 hover:text-white transition-colors duration-200 flex items-center"
                onClick={onClose}
                tabIndex={0}
              >
                Partners
              </Link>
              <Link
                to="/roadmap"
                className="w-full py-3 px-4 text-cyan-400 hover:text-white transition-colors duration-200 flex items-center"
                onClick={onClose}
                tabIndex={0}
              >
                Roadmap
              </Link>
              <Link
                to="/faq"
                className="w-full py-3 px-4 text-cyan-400 hover:text-white transition-colors duration-200 flex items-center"
                onClick={onClose}
                tabIndex={0}
              >
                FAQ
              </Link>

              {user && (
                <Link
                  to="/dashboard"
                  className="w-full py-3 px-4 text-cyan-400 hover:text-white transition-colors duration-200 flex items-center"
                  onClick={onClose}
                  tabIndex={0}
                >
                  Dashboard
                </Link>
              )}
            </div>

            {/* Login/Logout CTA */}
            <div className="mt-auto p-4 border-t border-gray-800 pt-4">
              {user ? (
                <div className="space-y-3">
                  <Button
                    variant="outline"
                    className="w-full border-purple-500 text-purple-400 hover:bg-purple-500/10"
                    onClick={onClose}
                    tabIndex={0}
                  >
                    <Link to="/dashboard" className="w-full flex items-center justify-center">
                      Dashboard
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full border-red-500/50 text-red-400 hover:bg-red-500/10"
                    onClick={() => {
                      signOut();
                      onClose();
                    }}
                    tabIndex={0}
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                  </Button>
                </div>
              ) : (
                <Link to="/login" className="w-full" onClick={onClose}>
                  <Button
                    className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 hover:from-purple-700 hover:to-cyan-600 text-black font-bold py-3 px-4 rounded-lg flex items-center justify-center"
                    tabIndex={0}
                  >
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </Button>
                </Link>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>,
    document.body
  );
};

export default MobileMenu;
