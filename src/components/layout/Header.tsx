import { Link, useLocation } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/stores/authStore";
import { Music2, Menu } from "lucide-react";
import { useState } from "react";
import MobileMenu from "./MobileMenu";

const Header = () => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Check if we're on the song request page (contains '/request' in the path)
  const isSongRequestPage = location.pathname.includes('/request');

  // Don't render the header on song request pages
  if (isSongRequestPage) {
    return null;
  }

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="sticky top-0 z-40 w-full border-b border-cyan-900/10 bg-black/70 backdrop-blur-md">
      <div className="container flex h-16 items-center justify-between px-3 xs:px-4 sm:px-6">
        <div className="flex items-center gap-2">
          <Link
            to="/"
            className="flex items-center gap-2 text-xl sm:text-2xl font-bold bg-gradient-to-r from-purple-500 to-cyan-400 bg-clip-text text-transparent"
          >
            <Music2 className="h-5 w-5 sm:h-6 sm:w-6 text-purple-400" />
            PlayBeg
          </Link>
        </div>

        {/* Mobile menu button */}
        <div className="block md:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMobileMenu}
            className="text-cyan-400 hover:text-white transition-colors"
            aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
            aria-expanded={mobileMenuOpen}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>

        {/* Desktop navigation */}
        <nav className="hidden md:flex items-center gap-4">
          <Link to="/blog">
            <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
              Blog
            </Button>
          </Link>
          <Link to="/partner">
            <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
              Partners
            </Button>
          </Link>
          <Link to="/roadmap">
            <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
              Roadmap
            </Button>
          </Link>
          <Link to="/faq">
            <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
              FAQ
            </Button>
          </Link>
          {user ? (
            <div className="flex items-center gap-4">
              <Link to="/dashboard">
                <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
                  Dashboard
                </Button>
              </Link>
              <Button
                onClick={() => signOut()}
                variant="outline"
                className="border-purple-500 text-purple-400 hover:bg-purple-500/10"
              >
                Sign Out
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Link to="/login">
                <Button variant="ghost" className="text-cyan-400 hover:text-cyan-300">
                  Sign In
                </Button>
              </Link>
              {/* Signup button removed */}
            </div>
          )}
        </nav>
      </div>

      {/* Mobile Menu Component */}
      <MobileMenu isOpen={mobileMenuOpen} onClose={() => setMobileMenuOpen(false)} />
    </header>
  );
};

export default Header;
