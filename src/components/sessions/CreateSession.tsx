import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/stores/authStore";
import { useMusicStore } from "@/stores/musicStore";
import { useSubscription } from "@/hooks/useSubscription";
import { Loader2 } from "lucide-react";

interface CreateSessionProps {
  onBack: () => void;
  onSessionCreated: (sessionId: string) => void;
}

export default function CreateSession({ onBack, onSessionCreated }: CreateSessionProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { isAuthorized: isAppleMusicConnected } = useMusicStore();
  const { currentPlan, subscription } = useSubscription();
  const [sessionName, setSessionName] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateSession = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a session",
        variant: "destructive",
      });
      return;
    }

    if (!isAppleMusicConnected) {
      toast({
        title: "Apple Music Required",
        description: "You must connect your Apple Music account before creating a session",
        variant: "destructive",
      });
      return;
    }

    if (!sessionName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a session name",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      // Check if user already has an active session
      const { data: existingSessions, error: checkError } = await supabase
        .from("sessions")
        .select("id")
        .eq("dj_id", user.id)
        .eq("active", true);

      if (checkError) throw checkError;

      if (existingSessions && existingSessions.length > 0) {
        toast({
          title: "Active Session Exists",
          description: "You already have an active session. Please deactivate it before creating a new one.",
          variant: "destructive",
        });
        setIsCreating(false);
        return;
      }

      // Check if user is on free tier
      const isFreePlan = currentPlan && currentPlan.name === 'Free';

      // For free tier users, log that we're enforcing auto-approval
      if (isFreePlan) {
        console.log('Free tier user - enforcing auto_approval=true');
      }

      // Get duration from the subscription plan for paid users
      let durationMinutes = 20; // Default for free tier

      if (!isFreePlan && currentPlan && currentPlan.duration_hours) {
        // For paid plans, use the remaining time from the subscription instead of the full plan duration
        if (subscription?.current_period_end) {
          const endDate = new Date(subscription.current_period_end);
          const now = new Date();

          // If subscription is still active
          if (endDate > now) {
            // Calculate remaining minutes from subscription end date
            const diffMs = endDate.getTime() - now.getTime();
            durationMinutes = Math.floor(diffMs / (1000 * 60));
            console.log(`Using remaining subscription time: ${durationMinutes} minutes (${Math.floor(durationMinutes / 60)} hours and ${durationMinutes % 60} minutes)`);
          } else {
            // Fallback to full plan duration if subscription has expired (shouldn't happen)
            durationMinutes = Math.round(currentPlan.duration_hours * 60);
            console.log(`Subscription expired, using full plan duration: ${currentPlan.duration_hours} hours (${durationMinutes} minutes)`);
          }
        } else {
          // Fallback to full plan duration if no end date is available
          durationMinutes = Math.round(currentPlan.duration_hours * 60);
          console.log(`No subscription end date, using full plan duration: ${currentPlan.duration_hours} hours (${durationMinutes} minutes)`);
        }
      }

      // Create a new session with auto-approval always set to false (feature removed)
      // and appropriate duration_minutes based on subscription plan
      const sessionData = {
        name: sessionName,
        dj_id: user.id,
        active: true,
        // Auto-approval feature has been removed
        auto_approval: false,
        // Set duration_minutes based on subscription plan
        duration_minutes: durationMinutes
      };

      console.log('Creating session with data:', sessionData);

      const { data, error } = await supabase
        .from("sessions")
        .insert(sessionData)
        .select()
        .single();

      if (error) throw error;

      console.log('Session created successfully:', data);

      // Format duration for display
      const formatDuration = (minutes) => {
        if (minutes < 60) {
          return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
        } else {
          const hours = Math.floor(minutes / 60);

          if (hours >= 24) {
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;

            if (remainingHours === 0) {
              return `${days} day${days !== 1 ? 's' : ''}`;
            } else {
              return `${days} day${days !== 1 ? 's' : ''} and ${remainingHours} hour${remainingHours !== 1 ? 's' : ''}`;
            }
          } else {
            return `${hours} hour${hours !== 1 ? 's' : ''}`;
          }
        }
      };

      toast({
        title: "Success",
        description: `Session created successfully with a ${formatDuration(durationMinutes)} duration`,
      });

      // Notify parent component that session was created
      if (data) {
        onSessionCreated(data.id);
      }
    } catch (error) {
      console.error("Error creating session:", error);
      toast({
        title: "Error",
        description: "Failed to create session",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md">
      {/* Desktop Header - Only visible on desktop */}
      <CardHeader className="hidden md:block">
        <div className="flex justify-between items-center">
          <CardTitle className="text-white">Create New Session</CardTitle>
          <Button variant="outline" onClick={onBack}>
            Cancel
          </Button>
        </div>
      </CardHeader>

      {/* Mobile Header - Only visible on mobile */}
      <div className="md:hidden p-4 space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-white">Create New Session</h2>
          <Button variant="outline" size="sm" onClick={onBack}>
            Cancel
          </Button>
        </div>

        <div className="space-y-2">
          <Label htmlFor="sessionNameMobile" className="text-sm text-gray-400">Session Name</Label>
          <Input
            id="sessionNameMobile"
            placeholder="Enter a name for your session"
            value={sessionName}
            onChange={(e) => setSessionName(e.target.value)}
            className="w-full bg-gray-900/50 border-gray-700"
          />
        </div>

        <Button
          className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200 active:scale-[0.98] active:opacity-90 flex items-center justify-center"
          onClick={handleCreateSession}
          disabled={isCreating}
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Create Session"
          )}
        </Button>
      </div>

      {/* Desktop Content - Only visible on desktop */}
      <CardContent className="hidden md:block">
        <div className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="sessionName">Session Name</Label>
            <Input
              id="sessionName"
              placeholder="Enter a name for your session"
              value={sessionName}
              onChange={(e) => setSessionName(e.target.value)}
              className="bg-gray-900/50 border-gray-700"
            />
          </div>

          <Button
            className="w-full bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
            onClick={handleCreateSession}
            disabled={isCreating}
          >
            {isCreating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Session"
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
