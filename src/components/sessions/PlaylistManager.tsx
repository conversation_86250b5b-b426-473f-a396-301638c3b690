import { useState } from "react";
import { useAuth } from "@/stores/authStore";
import { useAppleMusicAuth } from "@/hooks/useAppleMusicAuth";
import { usePlaylistManager } from "@/hooks/usePlaylistManager";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, Music, RefreshCw, Check, CheckCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { PlaylistManagerProps } from "@/types/playlist";
import { PlaylistCard } from "./PlaylistCard";
import { CreatePlaylistForm } from "./CreatePlaylistForm";
import { PlaylistStatus } from "./PlaylistStatus";
import PlaylistSyncButton from "./PlaylistSyncButton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Trash2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export default function PlaylistManager({
  sessionId,
  onPlaylistSelected,
  className
}: PlaylistManagerProps & { className?: string }) {
  const { user } = useAuth();
  const { toast } = useToast();
  const { isAuthorized, authorize: connectAppleMusic } = useAppleMusicAuth();
  const [createPlaylistMode, setCreatePlaylistMode] = useState(false);
  const [playlistName, setPlaylistName] = useState("");
  const [playlistDescription, setPlaylistDescription] = useState("");

  const {
    playlists,
    selectedPlaylistId,
    isPlaylistsLoading,
    isCreatingPlaylist,
    playlist,
    isActivatingPlaylist,
    activePlaylist,
    isDeletingPlaylist,
    playlistToDelete,
    setPlaylistToDelete,
    fetchPlaylists,
    createPlaylist,
    activatePlaylist,
    deletePlaylist,
    selectPlaylist,
  } = usePlaylistManager(user?.id, onPlaylistSelected);

  const handleConnectAppleMusic = async () => {
    const connected = await connectAppleMusic();
    if (connected) {
      toast({
        title: "Success",
        description: "Apple Music connected successfully!",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to connect to Apple Music. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCreatePlaylist = async () => {
    await createPlaylist(playlistName, playlistDescription, isAuthorized, sessionId);
    setCreatePlaylistMode(false);
    setPlaylistName("");
    setPlaylistDescription("");
  };

  return (
    <div className={cn("w-full relative flex-grow", className)}>
      <Card className="w-full h-full bg-gray-800/80 border-purple-500/20">
        <CardContent className="p-3 sm:p-4 h-full flex flex-col space-y-2">
          {isPlaylistsLoading ? (
            <div className="flex items-center justify-center">
              <RefreshCw className="h-6 w-6 animate-spin" />
            </div>
          ) : !isAuthorized ? (
            <div className="flex items-center justify-between mb-4 p-2 bg-yellow-500/10 border border-yellow-500/20 rounded-md">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 text-yellow-500 mr-2" />
                <span className="text-sm text-yellow-200">Apple Music not connected</span>
              </div>
              <Button
                size="sm"
                variant="outline"
                className="text-xs border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/20"
                onClick={handleConnectAppleMusic}
              >
                Connect
              </Button>
            </div>
          ) : createPlaylistMode ? (
            <CreatePlaylistForm
              playlistName={playlistName}
              playlistDescription={playlistDescription}
              onNameChange={setPlaylistName}
              onDescriptionChange={setPlaylistDescription}
              onSubmit={handleCreatePlaylist}
              onCancel={() => setCreatePlaylistMode(false)}
              isCreating={isCreatingPlaylist}
              activePlaylist={activePlaylist}
            />
          ) : selectedPlaylistId && playlist ? (
            <ScrollArea className="flex-grow pr-2 custom-scrollbar">
              <div className="space-y-2">
                <div className="flex items-center justify-between gap-4 p-3 rounded-lg bg-gray-800 border border-green-500/20">
                  <div className="flex items-center gap-3 text-sm md:text-base text-green-300 font-medium">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span>
                      Active Playlist: <span className="font-semibold text-white">{playlist.name}</span>
                    </span>
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <button className="min-h-[36px] px-4 py-1.5 rounded-md bg-red-700 hover:bg-red-800 text-white text-sm font-medium transition-colors duration-150">
                              <Trash2 className="w-4 h-4 inline-block mr-2" />
                              Delete Playlist
                            </button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Playlist?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will delete the playlist from the PlayBeg database only. The playlist will remain in your Apple Music library.
                                Are you sure you want to continue?
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => deletePlaylist(playlist.id)}
                                className="bg-red-600 hover:bg-red-700"
                                disabled={isDeletingPlaylist}
                              >
                                {isDeletingPlaylist ? "Deleting..." : "Delete Playlist"}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs">Deletes from PlayBeg database only, not Apple Music</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <PlaylistStatus
                  playlist={playlist}
                  isAuthorized={isAuthorized}
                  onActivate={activatePlaylist}
                  isActivating={isActivatingPlaylist}
                  sessionId={sessionId}
                />

                {playlist.apple_music_playlist_id && (
                  <PlaylistSyncButton
                    playlistId={playlist.id}
                    appleMusicPlaylistId={playlist.apple_music_playlist_id}
                    isDisabled={!isAuthorized}
                    showButton={false}
                  />
                )}
              </div>
            </ScrollArea>
          ) : (
            <div className="flex items-center justify-between mb-4 border-b border-purple-500/10 pb-3 pt-1">
              <h3 className="text-lg font-semibold">Select a Playlist</h3>
              <Button
                size="sm"
                onClick={() => setCreatePlaylistMode(true)}
                disabled={!isAuthorized}
                className="self-center bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
              >
                Create New Playlist
              </Button>
            </div>
          )}
          {!selectedPlaylistId && !createPlaylistMode && (
            <div className="flex-grow">
              {playlists.length > 0 ? (
                <ScrollArea className="flex-grow pr-2 custom-scrollbar">
                  <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3">
                    {playlists.map((playlist) => (
                      <PlaylistCard
                        key={playlist.id}
                        playlist={playlist}
                        onSelect={selectPlaylist}
                      />
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <div className="flex-grow flex items-center justify-center py-4 min-h-[180px]">
                  <div className="text-center p-6 border border-dashed border-gray-700 rounded-lg w-full max-w-md mx-auto">
                    <div className="mx-auto w-14 h-14 rounded-full bg-gray-800/80 flex items-center justify-center mb-4">
                      <Music className="h-7 w-7 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-300 mb-2">No playlists yet</h3>
                    <p className="text-gray-500 mb-4">
                      {isAuthorized
                        ? "Create a playlist to start adding songs"
                        : "Connect Apple Music to create playlists"}
                    </p>
                    {isAuthorized && (
                      <Button
                        onClick={() => setCreatePlaylistMode(true)}
                        className="mt-2 bg-gradient-to-r from-purple-600 to-cyan-600 hover:from-purple-700 hover:to-cyan-700 text-white font-semibold transition-colors duration-200"
                      >
                        Create New Playlist
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
