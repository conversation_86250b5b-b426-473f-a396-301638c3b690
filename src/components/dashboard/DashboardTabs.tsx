import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import SessionDetail from "@/components/sessions/SessionDetail";
import CreateSession from "@/components/sessions/CreateSession";
import SubscriptionManager from "@/components/subscription/SubscriptionManager";
import ProfileSettings from "@/components/profile/ProfileSettings";
import { useAuth } from "@/stores/authStore";
import { useState } from "react";
import { SessionsList } from "@/components/dashboard/SessionsList";
import { SessionWithSettings } from "@/types/session";

interface DashboardTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
  isAppleMusicConnected: boolean;
  selectedSessionId: string | null;
  onSelectSession: (sessionId: string | null) => void;
  viewMode?: string | null;
  onViewModeChange?: (view: string | null) => void;
  // Pass sessions and loading from parent to avoid duplicate hook calls
  sessions: SessionWithSettings[];
  loading: boolean;
  onSessionCreated: () => void;
  onSessionDeleted: () => void;
}

function DashboardTabs({
  activeTab,
  onTabChange,
  isAppleMusicConnected,
  selectedSessionId,
  onSelectSession,
  viewMode,
  onViewModeChange,
  sessions,
  loading,
  onSessionCreated,
  onSessionDeleted,
}: DashboardTabsProps) {
  const { user } = useAuth();
  console.log('✅ DASHBOARD_TABS: Using sessions from parent (no duplicate hook), count:', sessions.length);

  if (!user) {
    return null;
  }

  return (
    <Tabs defaultValue="sessions" value={activeTab} onValueChange={onTabChange}>
      <div className="px-4 md:px-0 overflow-x-auto whitespace-nowrap">
        <TabsList className="bg-gray-900/70 border border-purple-500/20 p-1 rounded-xl w-full flex gap-1">
          <TabsTrigger
            value="sessions"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/20 data-[state=active]:to-cyan-500/20 data-[state=active]:border-purple-500/30 data-[state=active]:border rounded-lg px-4 py-2 flex-1"
          >
            Sessions
          </TabsTrigger>
          <TabsTrigger
            value="subscription"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/20 data-[state=active]:to-cyan-500/20 data-[state=active]:border-purple-500/30 data-[state=active]:border rounded-lg px-4 py-2 flex-1"
          >
            Plans
          </TabsTrigger>
          <TabsTrigger
            value="settings"
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/20 data-[state=active]:to-cyan-500/20 data-[state=active]:border-purple-500/30 data-[state=active]:border rounded-lg px-4 py-2 flex-1"
          >
            Profile
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="sessions" className="mt-4 md:mt-6 px-0 md:px-0">
        {selectedSessionId === 'new' ? (
          <CreateSession
            onBack={() => onSelectSession(null)}
            onSessionCreated={(sessionId) => {
              // Refresh the sessions list
              onSessionCreated();
              // Stay on the sessions list page instead of navigating to the new session
              onSelectSession(null);
            }}
          />
        ) : selectedSessionId ? (
          <SessionDetail
            sessionId={selectedSessionId}
            onBack={() => onSelectSession(null)}
          />
        ) : (
          <SessionsList
            sessions={sessions}
            onSelectSession={onSelectSession}
            loading={loading}
            viewMode={viewMode}
            onViewModeChange={onViewModeChange}
            onSessionDeleted={onSessionDeleted}
          />
        )}
      </TabsContent>

      <TabsContent value="subscription" className="mt-4 md:mt-6 px-4 md:px-0">
        <SubscriptionManager />
      </TabsContent>

      <TabsContent value="settings" className="mt-4 md:mt-6 px-4 md:px-0">
        <Card className="modern-card">
          <CardHeader className="pb-2">
            <CardTitle className="text-white">DJ Profile</CardTitle>
            <CardDescription className="text-gray-300">
              Manage your DJ account preferences and profile
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">
            <ProfileSettings userId={user.id} userEmail={user.email} />
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}

export default React.memo(DashboardTabs);
