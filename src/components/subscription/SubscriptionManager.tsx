import { useState, useEffect } from "react";
import { useSubscription } from "@/hooks/useSubscription";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Clock, AlertCircle, Lock, Calendar, CreditCard, Timer } from "lucide-react";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/stores/authStore";
import { useSessionDuration } from "@/hooks/useSessionDuration";
import { TimeRemainingDisplay } from "@/components/ui/time-remaining-display";
import { formatDuration as formatDurationFn } from "@/hooks/use-time-remaining";
import { Tables } from "@/integrations/supabase/types";

type Session = Tables<"sessions">;

const SubscriptionManager = () => {
  const {
    plans,
    subscription,
    currentPlan,
    latestPurchase,
    hasActiveSubscription,
    isSubscriptionExpired,
    isLoading: isPlansLoading,
    isCheckoutLoading,
    createCheckoutSession,
  } = useSubscription();

  console.log("💰 SubscriptionManager component initialized");
  console.log("📊 Subscription data:", subscription ? JSON.stringify(subscription, null, 2) : "No subscription data");
  console.log("📊 Current plan:", currentPlan ? JSON.stringify(currentPlan, null, 2) : "No current plan");
  console.log("📊 Has active subscription:", hasActiveSubscription);
  console.log("📊 Is subscription expired:", isSubscriptionExpired ? isSubscriptionExpired() : false);

  const { toast } = useToast();
  const { user } = useAuth();
  const [processingPlanId, setProcessingPlanId] = useState<string | null>(null);
  const [activeSession, setActiveSession] = useState<Session | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState<boolean>(false);

  // Use the hook for session duration
  const { remainingTime, percentageRemaining, isNearExpiration } = useSessionDuration(activeSession);

  // Check if user is on a free plan
  const isFreePlan = currentPlan?.name === 'Free';
  console.log("📊 Is free plan:", isFreePlan);

  // Force refresh on interval to update UI based on expiration status
  useEffect(() => {
    // Set up an interval to check subscription status every minute
    const intervalId = setInterval(() => {
      if (isSubscriptionExpired && isSubscriptionExpired()) {
        console.log("🔄 Detected expired subscription in UI interval check");
      }
    }, 60000);

    return () => clearInterval(intervalId);
  }, [isSubscriptionExpired]);

  // Fetch active session
  useEffect(() => {
    const fetchActiveSession = async () => {
      if (!user?.id) {
        console.log("⏭️ Skipping active session fetch: No user ID");
        return;
      }

      console.log(`🔍 Fetching active session for user: ${user.id}`);
      setIsLoadingSession(true);

      try {
        const { data, error } = await supabase
          .from('sessions')
          .select('*')
          .eq('dj_id', user.id)
          .eq('active', true)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (error) {
          console.error("❌ Error fetching active session:", error);
        }

        console.log("📊 Active session:", data ? JSON.stringify(data, null, 2) : "No active session found");
        setActiveSession(data || null);
      } catch (error) {
        console.error("❌ Failed to fetch active session:", error);
        console.error(error.stack || "No stack trace available");
      } finally {
        setIsLoadingSession(false);
      }
    };

    fetchActiveSession();

    // Set up a listener for session changes
    console.log("🔌 Setting up realtime listener for session changes");
    const channel = supabase
      .channel('session_changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sessions',
        filter: user?.id ? `dj_id=eq.${user.id}` : undefined,
      }, (payload) => {
        console.log("🔄 Received session change event:", payload);
        fetchActiveSession();
      })
      .subscribe();

    return () => {
      console.log("🔌 Removing session listener channel");
      supabase.removeChannel(channel);
    };
  }, [user?.id]);

  const handleCheckout = async (planId: string) => {
    if (isCheckoutLoading) {
      console.log("⏭️ Checkout already in progress, skipping");
      return;
    }

    console.log(`🛒 Starting checkout for plan: ${planId}`);
    setProcessingPlanId(planId);
    try {
      console.log(`🔄 Calling createCheckoutSession with planId: ${planId}`);
      await createCheckoutSession(planId);
    } catch (error) {
      console.error("❌ Failed to initiate checkout:", error);
      console.error(error.stack || "No stack trace available");
      toast({
        title: "Checkout Failed",
        description: "Could not initiate the checkout process. Please try again.",
        variant: "destructive",
      });
    } finally {
      setProcessingPlanId(null);
    }
  };

  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
    }).format(amount / 100);
  };

  const formatDuration = (hours: number) => {
    // For fractional hours (like 0.33), convert to minutes
    if (hours < 1) {
      const minutes = Math.round(hours * 60);
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }

    if (hours === 24) return "24 hours";
    if (hours === 48) return "48 hours";
    if (hours === 168) return "7 days";

    // This was the problematic part - for duration_hours, not minutes
    // Fix by ensuring we're not multiplying the hours value by 60 here
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  };

  const getRemainingTime = () => {
    if (currentPlan?.name === 'Free') {
      console.log("⏰ Free plan has no end date, always active");
      return "Always active";
    }

    if (isSubscriptionExpired && isSubscriptionExpired()) {
      console.log("⏰ Subscription has expired");
      return "Expired";
    }

    if (!subscription?.current_period_end) {
      console.log("⏰ No subscription end date found");
      return "No expiration";
    }

    const endDate = new Date(subscription.current_period_end);
    const now = new Date();

    console.log(`⏰ Current time: ${now.toISOString()}`);
    console.log(`⏰ Subscription end date: ${endDate.toISOString()}`);

    // If already expired
    if (endDate <= now) {
      console.log("⏰ Subscription has expired");
      return "Expired";
    }

    const diffMs = endDate.getTime() - now.getTime();
    const diffHrs = Math.floor(diffMs / (1000 * 60 * 60));

    console.log(`⏰ Time remaining: ${diffHrs} hours`);

    // Updated to show days + hours when over 24 hours
    if (diffHrs >= 24) {
      const diffDays = Math.floor(diffHrs / 24);
      const remainingHours = diffHrs % 24;

      const endDateFormatted = `${endDate.toLocaleDateString()} ${endDate.toLocaleTimeString()}`;

      console.log(`⏰ Formatted as: ${diffDays} days, ${remainingHours} hours remaining`);

      if (remainingHours === 0) {
        return `${diffDays} day${diffDays !== 1 ? 's' : ''} remaining (ends ${endDateFormatted})`;
      } else {
        return `${diffDays} day${diffDays !== 1 ? 's' : ''} ${remainingHours}h remaining (ends ${endDateFormatted})`;
      }
    } else {
      return `${diffHrs} hour${diffHrs !== 1 ? 's' : ''} remaining (ends ${endDate.toLocaleDateString()} ${endDate.toLocaleTimeString()})`;
    }
  };

  // Show loading state only when both plans and subscription data are loading
  if (isPlansLoading || isLoadingSession) {
    console.log("⏳ Subscription data still loading");
    return (
      <div className="flex justify-center p-8">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
          className="h-10 w-10 border-4 border-t-purple-500 border-r-cyan-500 border-b-purple-500/30 border-l-cyan-500/30 rounded-full"
        />
      </div>
    );
  }

  // Function to get badge for popular plan
  const getPopularBadge = (plan) => {
    // Check if it's the 48-hour plan (typically the most popular)
    if (plan.duration_hours === 48) {
      return (
        <Badge className="absolute top-2 right-0 left-0 mx-auto w-max bg-gradient-to-r from-green-400 to-emerald-500 text-white border-none font-medium py-1 px-3 z-10 shadow-md">
          Most Popular
        </Badge>
      );
    }
    return null;
  };

  // Function to get description for plan
  const getPlanDescription = (plan) => {
    if (plan.name === 'Free') {
      return "Perfect for trying out PlayBeg.";
    } else if (plan.duration_hours === 24) {
      return "Pay-per-use for a single event.";
    } else if (plan.duration_hours === 48) {
      return "Weekend coverage for back-to-back events.";
    } else if (plan.duration_hours === 168) { // 7 days
      return "Best value for multiple events.";
    }
    return plan.description;
  };

  // Function to get plan features
  const getPlanFeatures = (plan) => {
    const features = [];

    if (plan.name === 'Free') {
      features.push("20 minutes access only");
      features.push("Only 3 song requests per session");
      features.push("Manual song approval");
      features.push("Genre blocking");
      features.push("Apple Music integration");
    } else if (plan.duration_hours === 24) {
      features.push("Full access for 24 hours");
      features.push("Up to 100 song requests per session");
      features.push("Start and end playlists as needed");
      features.push("Manual song approval");
      features.push("Genre blocking");
      features.push("🎊 Wedding Display Mode with 6 templates");
      features.push("Custom sponsor branding");
      features.push("Apple Music integration");
      features.push("Perfect for DJs who need PlayBeg for a single gig");
    } else if (plan.duration_hours === 48) {
      features.push("Full access for 48 hours");
      features.push("Up to 100 song requests per session");
      features.push("Start and end playlists as needed");
      features.push("Manual song approval");
      features.push("Genre blocking");
      features.push("🎊 Wedding Display Mode with 6 templates");
      features.push("Custom sponsor branding");
      features.push("Full request history");
      features.push("Apple Music integration");
      features.push("Ideal for weekend events");
    } else if (plan.duration_hours === 168) { // 7 days
      features.push("Full access for 7 days");
      features.push("Up to 100 song requests per session");
      features.push("Start and end playlists as needed");
      features.push("Manual song approval");
      features.push("Genre blocking");
      features.push("🎊 Wedding Display Mode with 6 templates");
      features.push("Custom sponsor branding");
      features.push("Full request history");
      features.push("Multiple active sessions");
      features.push("VIP request prioritization");
      features.push("Apple Music integration");
      features.push("Best for DJs with multiple gigs in a week");
    }

    return features;
  };

  // Function to get button text for plan
  const getButtonText = (plan) => {
    if (plan.name === 'Free') {
      return "Start Free Session";
    } else if (plan.duration_hours === 24) {
      return hasActiveSubscription ? "Extend 24-Hour Access" : "Get 24-Hour Access";
    } else if (plan.duration_hours === 48) {
      return hasActiveSubscription ? "Extend 48-Hour Access" : "Get 48-Hour Access";
    } else if (plan.duration_hours === 168) { // 7 days
      return hasActiveSubscription ? "Extend 7-Days Access" : "Get 7-Days Access";
    }
    return hasActiveSubscription ? "Extend Access" : "Get Access";
  };

  return (
    <div className="space-y-6 md:space-y-6">
      {/* Desktop Header - Only visible on desktop */}
      <div className="hidden md:block">
        <h2 className="text-2xl font-bold text-white">DJ Session Access</h2>
        {(subscription || isFreePlan) && (
          <div className="mt-2">
            {hasActiveSubscription || isFreePlan ? (
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                  <CheckCircle className="h-3.5 w-3.5 mr-1" />
                  Active
                </Badge>
                {currentPlan && (
                  <p className="text-sm text-gray-400">
                    {currentPlan.name} - {getRemainingTime()}
                  </p>
                )}
              </div>
            ) : (
              <Badge variant="outline" className="bg-red-500/10 text-red-500 border-red-500/20">
                <AlertCircle className="h-3.5 w-3.5 mr-1" />
                Inactive
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Mobile Header - Only visible on mobile */}
      <div className="md:hidden px-4 pt-2">
        <h2 className="text-xl font-bold text-white mb-2">DJ Session Access</h2>
        {(subscription || isFreePlan) && hasActiveSubscription && (
          <div className="bg-black/40 rounded-lg p-4 text-sm text-white space-y-1">
            <p className="font-semibold flex items-center">
              <CheckCircle className="h-3.5 w-3.5 mr-2 text-green-500" />
              {currentPlan?.name} - Active
            </p>
            <p>{currentPlan?.duration_hours ? `${formatDuration(currentPlan.duration_hours)} Access` : ''}
              {subscription?.current_period_end && !isSubscriptionExpired() ?
                ` – ${getRemainingTime().split('(')[0]}` : ''}
            </p>
            {subscription?.current_period_end && !isSubscriptionExpired() && (
              <p className="text-xs text-muted-foreground">
                Ends {new Date(subscription.current_period_end).toLocaleDateString()} {new Date(subscription.current_period_end).toLocaleTimeString()}
              </p>
            )}
          </div>
        )}
        {(subscription || isFreePlan) && !hasActiveSubscription && !isFreePlan && (
          <div className="bg-black/40 rounded-lg p-4 text-sm text-white space-y-1">
            <p className="font-semibold flex items-center">
              <AlertCircle className="h-3.5 w-3.5 mr-2 text-red-500" />
              Inactive
            </p>
            <p>Your subscription has expired</p>
          </div>
        )}
      </div>

      {/* Desktop Current Access Card - Only visible on desktop */}
      {(hasActiveSubscription || isFreePlan) && currentPlan && (
        <div className="hidden md:block">
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">Current Access</CardTitle>
              <CardDescription>
                {currentPlan.name === 'Free'
                  ? "You have basic access with the Free plan"
                  : subscription?.current_period_end && !isSubscriptionExpired()
                    ? `Your access is active until ${new Date(subscription.current_period_end).toLocaleDateString()} ${new Date(subscription.current_period_end).toLocaleTimeString()}`
                    : "Your access is active"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Subscription Access Section */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-400">Subscription Access</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">Active Until</span>
                  </div>
                  <span className="text-sm text-right">
                    {currentPlan.name === 'Free'
                      ? "Always active"
                      : subscription?.current_period_end && !isSubscriptionExpired()
                        ? new Date(subscription.current_period_end).toLocaleDateString()
                        : "N/A"}
                  </span>

                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-cyan-500" />
                    <span className="text-sm">Time Remaining</span>
                  </div>
                  <span className="text-sm text-right">{getRemainingTime()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Mobile Current Access Card - Only visible on mobile */}
      {(hasActiveSubscription || isFreePlan) && currentPlan && !isFreePlan && (
        <div className="md:hidden px-4 py-2 space-y-4">
          {!isFreePlan && subscription?.current_period_end && latestPurchase && (
            <div className="bg-black/40 rounded-lg p-4 text-sm text-white space-y-3">
              <p className="font-semibold">Latest Purchase</p>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-purple-500" />
                    <span>Plan:</span>
                  </div>
                  <span className="font-medium">{latestPurchase.name}</span>
                </div>

                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <Timer className="h-4 w-4 text-cyan-500" />
                    <span>Duration:</span>
                  </div>
                  <span className="font-medium">
                    {latestPurchase.duration_hours ? formatDuration(latestPurchase.duration_hours) : "N/A"}
                  </span>
                </div>

                <div className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-purple-500" />
                    <span>Amount:</span>
                  </div>
                  <span className="font-medium">
                    {formatPrice(latestPurchase.price_amount, latestPurchase.price_currency)}
                  </span>
                </div>
              </div>
            </div>
          )}

          <Button
            className="w-full bg-gradient-to-r from-purple-600 to-cyan-500 text-white font-bold"
            onClick={() => handleCheckout(plans.find(p => p.duration_hours === 48)?.id || plans[0]?.id)}
            disabled={isCheckoutLoading}
          >
            Extend Access
          </Button>
        </div>
      )}

      {/* Mobile Session Duration Section - Only show for Free Plan */}
      {isFreePlan && activeSession && activeSession.duration_minutes > 0 && (
        <div className="md:hidden px-4 py-2">
          <div className="bg-black/40 rounded-lg p-4 text-sm text-white space-y-3">
            <p className="font-semibold flex items-center gap-2">
              Active Session Duration
              {activeSession.name && (
                <Badge variant="outline" className="bg-purple-500/10 text-purple-500 border-purple-500/20">
                  {activeSession.name}
                </Badge>
              )}
            </p>

            <div className="space-y-2">
              <TimeRemainingDisplay
                createdAt={activeSession.created_at}
                durationMinutes={activeSession.duration_minutes}
                showProgressBar={true}
                showTotal={true}
                iconSize="md"
                className="mb-2"
              />

              <div className="text-xs text-gray-500">
                <span>Created: {new Date(activeSession.created_at).toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Session Duration Section - Only show for Free Plan */}
      {isFreePlan && activeSession && activeSession.duration_minutes > 0 && (
        <div className="hidden md:block">
          <Card className="bg-black/50 border-purple-500/20 backdrop-blur-md">
            <CardContent className="space-y-6 pt-6">
              <Separator className="bg-gray-800" />

              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-400 flex items-center gap-2">
                  Active Session Duration
                  {activeSession.name && (
                    <Badge variant="outline" className="bg-purple-500/10 text-purple-500 border-purple-500/20">
                      {activeSession.name}
                    </Badge>
                  )}
                </h4>

                <div className="space-y-2">
                  <TimeRemainingDisplay
                    createdAt={activeSession.created_at}
                    durationMinutes={activeSession.duration_minutes}
                    showProgressBar={true}
                    showTotal={true}
                    iconSize="md"
                    className="mb-2"
                  />

                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Created: {new Date(activeSession.created_at).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <Separator className="bg-gray-800" />

              {!isFreePlan && subscription?.current_period_end && latestPurchase && (
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-gray-400">Latest Purchase</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-purple-500" />
                      <span className="text-sm">Plan</span>
                    </div>
                    <span className="text-sm text-right">{latestPurchase.name}</span>

                    <div className="flex items-center gap-2">
                      <Timer className="h-4 w-4 text-cyan-500" />
                      <span className="text-sm">Duration</span>
                    </div>
                    <span className="text-sm text-right">
                      {latestPurchase.duration_hours ? formatDuration(latestPurchase.duration_hours) : "N/A"}
                    </span>

                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4 text-purple-500" />
                      <span className="text-sm">Amount</span>
                    </div>
                    <span className="text-sm text-right">
                      {formatPrice(latestPurchase.price_amount, latestPurchase.price_currency)}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Desktop Plans Header - Only visible on desktop */}
      <div className="hidden md:block space-y-4">
        <div>
          <h3 className="text-xl font-semibold text-white">
            {hasActiveSubscription ? "Extend Your Access" : "Get Access"}
          </h3>
          <p className="mt-2 text-gray-400 border-l-2 border-purple-500 pl-3 italic">
            There are no recurring subscription fees—you're only charged once per plan purchase, and you can extend your access as much as you need within the plan.
          </p>
        </div>

        {/* Desktop Plan Cards Layout */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {plans?.map((plan) => (
            <Card
              key={plan.id}
              className={`relative overflow-hidden ${
                plan.duration_hours === 48
                  ? 'bg-purple-900/50 border-violet-500/50'
                  : 'bg-gray-900/50 border-gray-800/80'
              } h-full flex flex-col`}
            >
              {getPopularBadge(plan)}

              <CardHeader className={`${plan.duration_hours === 48 ? 'pt-8' : ''}`}>
                <CardTitle className="text-white text-xl">
                  {plan.name === 'Free'
                    ? 'Free Mode'
                    : `Premium ${
                        plan.duration_hours === 24
                          ? '24-Hour'
                          : plan.duration_hours === 48
                            ? '48-Hour'
                            : 'Weekly'
                      }`
                  }
                </CardTitle>
                <CardDescription className="text-gray-300">
                  {getPlanDescription(plan)}
                </CardDescription>
              </CardHeader>

              <CardContent className="flex-grow">
                {plan.price_amount === 0 ? (
                  <div className="text-4xl font-bold text-white mb-6">
                    Free
                  </div>
                ) : (
                  <div className="mb-6">
                    <div className="text-4xl font-bold text-white flex items-end">
                      {formatPrice(plan.price_amount, plan.price_currency)}
                      <span className="text-sm text-gray-400 ml-1 mb-1">
                        {plan.duration_hours === 168
                          ? 'per week'
                          : `for ${plan.duration_hours} hours`}
                      </span>
                    </div>
                  </div>
                )}

                <ul className="space-y-2">
                  {getPlanFeatures(plan).map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className={`h-5 w-5 mt-0.5 ${
                        plan.duration_hours === 48
                          ? 'text-cyan-400'
                          : 'text-green-500'
                      }`} />
                      <span className="text-gray-300">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>

              <CardFooter className="pt-6">
                {plan.price_amount === 0 ? (
                  <Button
                    variant="locked"
                    className="w-full"
                    disabled={true}
                  >
                    <Lock className="w-4 h-4 mr-2" />
                    Free Plan
                  </Button>
                ) : (
                  <Button
                    className={`w-full ${
                      plan.duration_hours === 48
                        ? 'bg-gradient-to-r from-violet-600 to-cyan-500 hover:from-violet-700 hover:to-cyan-600'
                        : 'bg-gradient-to-r from-purple-600 to-indigo-500 hover:from-purple-700 hover:to-indigo-600'
                    }`}
                    onClick={() => handleCheckout(plan.id)}
                    disabled={isCheckoutLoading || processingPlanId === plan.id}
                  >
                    {processingPlanId === plan.id ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                          className="h-4 w-4 border-2 border-t-white border-r-white/50 border-b-white/0 border-l-white/0 rounded-full mr-2"
                        />
                        Processing...
                      </>
                    ) : (
                      getButtonText(plan)
                    )}
                  </Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      {/* Mobile Plans Header - Only visible on mobile */}
      <div className="md:hidden px-4 py-4">
        <h3 className="text-lg font-semibold text-white mb-2">
          {hasActiveSubscription ? "Extend Your Access" : "Get Access"}
        </h3>
        <p className="text-sm text-gray-400 border-l-2 border-purple-500 pl-3 italic mb-6">
          There are no recurring subscription fees—you're only charged once per plan purchase.
        </p>

        {/* Mobile Plan Cards Layout */}
        <div className="space-y-6">
          {plans?.filter(plan => plan.price_amount > 0).map((plan) => (
            <Card
              key={plan.id}
              className="w-full bg-gray-900/60 backdrop-blur-md border border-gray-700/40 rounded-xl p-4 space-y-4 shadow-sm relative mb-0"
            >
              {plan.duration_hours === 48 && (
                <div className="inline-block bg-amber-400/90 text-black text-xs font-bold px-2 py-0.5 rounded-sm mb-2">
                  MOST POPULAR
                </div>
              )}

              <h3 className="text-lg font-semibold text-white">
                {plan.name === 'Free'
                  ? 'Free Mode'
                  : `Premium ${
                      plan.duration_hours === 24
                        ? '24-Hour'
                        : plan.duration_hours === 48
                          ? '48-Hour'
                          : 'Weekly'
                    }`
                }
              </h3>

              <p className="text-sm font-normal text-gray-300">
                {formatPrice(plan.price_amount, plan.price_currency)} for {plan.duration_hours === 168 ? '7 days' : `${plan.duration_hours} hours`}
              </p>

              <ul className="text-sm font-normal text-muted-foreground space-y-1 list-disc list-inside">
                {getPlanFeatures(plan).filter(feature =>
                  !feature.includes("Perfect for DJs") &&
                  !feature.includes("Ideal for weekend") &&
                  !feature.includes("Best for DJs with")
                ).map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>

              {plan.duration_hours === 24 && (
                <p className="text-xs text-gray-400 italic mt-2">
                  Perfect for a single gig
                </p>
              )}

              {plan.duration_hours === 48 && (
                <p className="text-xs text-gray-400 italic mt-2">
                  Ideal for weekend events
                </p>
              )}

              {plan.duration_hours === 168 && (
                <p className="text-xs text-gray-400 italic mt-2">
                  Best for DJs with multiple gigs in a week
                </p>
              )}

              <Button
                className={`w-full h-11 ${
                  plan.duration_hours === 48
                    ? 'bg-gradient-to-r from-violet-600 to-cyan-500 hover:from-violet-700 hover:to-cyan-600'
                    : 'bg-gradient-to-r from-purple-600 to-indigo-500 hover:from-purple-700 hover:to-indigo-600'
                } text-white font-semibold active:scale-[0.98] active:opacity-90 transition-all duration-200`}
                onClick={() => handleCheckout(plan.id)}
                disabled={isCheckoutLoading || processingPlanId === plan.id}
              >
                {processingPlanId === plan.id ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                      className="h-4 w-4 border-2 border-t-white border-r-white/50 border-b-white/0 border-l-white/0 rounded-full mr-2"
                    />
                    Processing...
                  </>
                ) : (
                  getButtonText(plan)
                )}
              </Button>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubscriptionManager;
