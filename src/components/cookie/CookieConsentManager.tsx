import { useState, useEffect } from 'react';
import SimpleCookieConsent from './SimpleCookieConsent';
import CookieSettingsModal from './CookieSettingsModal';
import { getCookie, logConsent } from '@/utils/cookieHelpers';
import { updateAnalyticsConsent } from '@/utils/simpleAnalytics';

const CookieConsentManager = () => {
  const [showSettings, setShowSettings] = useState(false);
  
  // <PERSON>le accepting all cookies
  const handleAcceptAll = async () => {
    logConsent('all', { analytics: true });
    try {
      await updateAnalyticsConsent(true);
    } catch (error) {
      console.log('Analytics setup failed, continuing without analytics');
    }
  };

  // <PERSON>le accepting only necessary cookies
  const handleAcceptNecessary = async () => {
    logConsent('necessary', { analytics: false });
    try {
      await updateAnalyticsConsent(false);
    } catch (error) {
      console.log('Analytics disable failed, continuing');
    }
  };

  // <PERSON>le saving custom settings
  const handleSaveSettings = async (settings: { analytics: boolean }) => {
    logConsent('custom', settings);
    try {
      await updateAnalyticsConsent(settings.analytics);
    } catch (error) {
      console.log('Analytics setup failed, continuing without analytics');
    }
  };
  
  // Expose a global function to open cookie settings
  useEffect(() => {
    // Add a global function to open cookie settings
    window.openCookieSettings = () => {
      setShowSettings(true);
    };
    
    return () => {
      // Clean up
      delete window.openCookieSettings;
    };
  }, []);
  
  return (
    <>
      <SimpleCookieConsent 
        onAcceptAll={handleAcceptAll}
        onAcceptNecessary={handleAcceptNecessary}
        onOpenSettings={() => setShowSettings(true)}
      />
      
      <CookieSettingsModal 
        open={showSettings}
        onOpenChange={setShowSettings}
        onSave={handleSaveSettings}
      />
    </>
  );
};

// Add global type definition
declare global {
  interface Window {
    openCookieSettings?: () => void;
  }
}

export default CookieConsentManager;
