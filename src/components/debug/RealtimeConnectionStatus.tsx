import { useState, useEffect } from 'react';
import { realtimeManager } from '@/utils/realtimeConnectionManager';
import { useAuth } from '@/stores/authStore';

export function RealtimeConnectionStatus() {
  const { user } = useAuth();
  const [activeChannels, setActiveChannels] = useState<string[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');

  useEffect(() => {
    // Update active channels every second
    const interval = setInterval(() => {
      const channels = realtimeManager.getActiveChannels();
      setActiveChannels(channels);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs max-w-xs">
      <div className="font-bold mb-2">🔌 Realtime Status</div>
      <div>User: {user?.id ? 'Authenticated' : 'Not authenticated'}</div>
      <div>Active Channels: {activeChannels.length}</div>
      {activeChannels.length > 0 && (
        <div className="mt-2">
          <div className="font-semibold">Channels:</div>
          {activeChannels.map(channel => (
            <div key={channel} className="text-green-400 truncate">
              • {channel}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
