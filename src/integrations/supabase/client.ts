// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://lbwqwecunykdhenvdrbz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxid3F3ZWN1bnlrZGhlbnZkcmJ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE1OTA2MDgsImV4cCI6MjA1NzE2NjYwOH0.COGiTdAJn1HhhrXVHrQ1LIoFxsIY5AQoMDRN1kOwTFs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Extend the Window interface to include our supabase client
declare global {
  interface Window {
    supabaseClient: any;
  }
}

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
  realtime: {
    params: {
      eventsPerSecond: 10, // Limit events to prevent overload
    },
    heartbeatIntervalMs: 30000, // 30 seconds
    reconnectAfterMs: (tries: number) => Math.min(tries * 1000, 10000), // Exponential backoff with max 10s
  },
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  },
});

// Make the Supabase client available globally for components that need it
// This is used by the RequestHeader component to refresh session status
if (typeof window !== 'undefined') {
  window.supabaseClient = supabase;
}

// Helper function to check if storage buckets exist and are accessible
export const initializeStorage = async () => {
  try {
    // Check if we can access the storage buckets
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Storage initialization error:', listError.message);
      return false;
    }

    // Check if the profile pictures bucket exists
    const profileBucketExists = buckets?.some(bucket => bucket.name === 'dj-profile-pictures');
    const sponsorBucketExists = buckets?.some(bucket => bucket.name === 'sponsor-logo-pictures');

    return profileBucketExists && sponsorBucketExists;
  } catch (error) {
    console.error('Error checking storage:', error);
    return false;
  }
};
