
import { RouterProvider } from 'react-router-dom';
import { ThemeProvider } from '@/context/ThemeContext';
import { MusicErrorBoundary } from '@/components/error/MusicErrorBoundary';
import { Toaster } from '@/components/ui/toaster';
import CookieConsentManager from '@/components/cookie/CookieConsentManager';
import router from './routes';
import { useEffect } from 'react';
import { initAnalytics } from '@/utils/simpleAnalytics';
import { HelmetProvider } from 'react-helmet-async';
import { useAuthStore, initializeAuthListener } from '@/stores/authStore';

export default function App() {
  const initialize = useAuthStore(state => state.initialize);

  // Initialize auth store and analytics
  useEffect(() => {
    const initializeApp = async () => {
      // Initialize auth state from Supabase
      initialize();

      // Set up auth listener
      const subscription = initializeAuthListener();

      // Initialize analytics based on cookie consent
      try {
        await initAnalytics();
      } catch (error) {
        console.log('Analytics initialization failed, continuing without analytics');
      }

      return subscription;
    };

    let subscription: any;
    initializeApp().then((sub) => {
      subscription = sub;
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [initialize]);

  return (
    <HelmetProvider>
      <ThemeProvider>
        <MusicErrorBoundary>
          <RouterProvider router={router} />
        </MusicErrorBoundary>
        <Toaster />
        <CookieConsentManager />
      </ThemeProvider>
    </HelmetProvider>
  );
}
